// =====================================
// search-analytics.interfaces.ts
// =====================================

import {AdvertStatusEnum, SoldStatusEnum} from "../../../enums";
import { BaseSearchDTO } from "../API";

// Filter DTOs
export interface AdminCustomerSearchFilters {
  customerName?: string;
  customerEmail?: string;
  assignedTo?: string;
  fromDate?: Date;
  toDate?: Date;
  minEffectivenessRate?: number;
  maxEffectivenessRate?: number;
}

export interface AdminAdvertSearchFilters {
  vrm?: string;
  advertStatus?: string;
  soldStatus?: string;
  fromDate?: Date;
  toDate?: Date;
  minNotificationRate?: number;
  maxNotificationRate?: number;
  vendorId?: string;
}

// Search DTOs
export interface CustomerAnalyticsSearchDTO extends BaseSearchDTO {
  filters: AdminCustomerSearchFilters;
}

export interface AdvertAnalyticsSearchDTO extends BaseSearchDTO {
  filters: AdminAdvertSearchFilters;
}

// Main Analytics DTOs
export interface SavedSearchStatsDTO {
  savedSearchId: string;
  searchCriteria: string;
  notificationsEnabled: boolean;
  totalMatchingAdverts: number;
  totalAdvertsNotifiedAbout: number;
  notificationEffectivenessRate: number;
  lastNotificationSent?: Date;
  createdDate: Date;
}

export interface AdminCustomerSearchAnalyticsDTO {
  customerId: string;
  customerName: string;
  customerEmail: string;
  assignedTo?: string;
  totalSavedSearches: number;
  activeNotificationSearches: number;
  totalMatchingAdverts: number;
  totalAdvertsNotifiedAbout: number;
  notificationEffectivenessRate: number;
  savedSearches: SavedSearchStatsDTO[];
}

export interface AdvertNotificationStatsDTO {
  advertId: string;
  vrm: string;
  description: string;
  customerName: string;
  customerId: string;
  added: Date;
  updated: Date;
  advertStatus: AdvertStatusEnum;
  soldStatus: SoldStatusEnum;
  matchedInSavedSearchCount: number;
  matchedInUnsavedSearchCount: number;
  contactsNotifiedAbout: number;
  notificationEffectivenessRate: number;
  notificationEffectiveness: string;

  // Email metrics properties
  emailsSent: number;
  emailsDelivered: number;
  emailsOpened: number;
  emailsClicked: number;
  emailsBounced: number;
  emailsBlocked: number;

  // Email engagement rates (calculated properties)
  emailDeliveryRate: number;
  emailOpenRate: number;
  emailClickRate: number;
  emailBounceRate: number;
  emailEngagementScore: number;
  combinedEffectivenessRate: number;

  isOpen: boolean;
}

export interface SystemMetricSummaryDTO {
  name: string;
  value: string;
  description: string;
  trendIndicator: string;
}

export interface AdminSearchSystemOverviewDTO {
  totalCustomers: number;
  customersWithNoSearches: number;
  totalSavedSearches: number;
  activeSavedSearches: number;
  totalActiveAdverts: number;
  totalAdvertsNotifiedAbout: number;
  customerEngagementRate: number;
  systemHealthStatus: string;
  keyMetrics: SystemMetricSummaryDTO[];
}

export interface AdminStatVendorDTO {
  id: string;
  vendorName: string;
}

export interface AdminStatBasicCustomerDTO {
  id: string;
  customerName: string;
  isBuyer?: boolean;
  isSeller?: boolean;
  liveAdverts?: number;
  totalAdverts?: number;
  lastLogin?: Date;
  lastAdvert?: Date;
}
