import {AdvertSearchService, RoleGuardService} from "../../../../services";
import {FilterListItem} from "../../../../services/models/filterListitem";
import {LoggerService} from "../../../../global/services";
import {
  AdvertFilterValueTypeEnum,
  AdvertSearchFieldEnum,
  SaleTypeFilter,
  SearchFilterTypeEnum,
  SortingFilter,
  SortingType,
  UserRolesEnum
} from "../../../../global/enums";
import {AdvertSearchCount, AdvertSearchCountList, AdvertSearchDTO, User} from "../../../../global/interfaces";

export class AdvertSearchFilters {

  constructor(private advertSearchService: AdvertSearchService,
              private authService: RoleGuardService,
              private logService: LoggerService) {

    this.SearchOptions = {
      saleFilter: SaleTypeFilter.All,
      sorting: SortingType.NewlyListed,
      sortingFilter: SortingFilter.AnyTime,
      runner: -1,
      catStatus: -1,
      page: 1
    };
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  public Keywords: string;
  public SearchOptions: {
    saleFilter: SaleTypeFilter,
    sorting: SortingType,
    sortingFilter: SortingFilter,
    runner: number,
    catStatus: number,
    page: number
  };

  public CurrentSearchDTO: AdvertSearchDTO;

  public filterList: FilterListItem[] = [];

  public GetSearchDTO(): AdvertSearchDTO {
    const makeIds = this.getSelectedIds(AdvertSearchFieldEnum.Make);
    const modelIds = this.getSelectedIds(AdvertSearchFieldEnum.Model);

    // Get range values - either from custom inputs or range IDs
    const priceFilter = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Price);
    const mileageFilter = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Mileage);
    const capacityFilter = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Capacity);

    //console.log("*** (( Age range: ",  this.getMinMax(AdvertSearchFieldEnum.Age))

    const filterDTO = {
      filters: {
        keywords: this.Keywords,
        vehicleTypeIds: this.getSelectedIds(AdvertSearchFieldEnum.VehicleType),
        makeIds,
        modelIds,
        derivativeIds: this.getSelectedIds(AdvertSearchFieldEnum.Deriv),
        fuelTypeIds: this.getSelectedIds(AdvertSearchFieldEnum.Fuel),
        transmissionTypeIds: this.getSelectedIds(AdvertSearchFieldEnum.Transmission),
        bodyTypeIds: this.getSelectedIds(AdvertSearchFieldEnum.BodyType),
        plateIds: this.getSelectedIds(AdvertSearchFieldEnum.Plate),
        ageRange: this.getAgeRangeInMonths(),
        doors: this.getSelectedIds(AdvertSearchFieldEnum.Doors),
        vatStatusIds: this.getSelectedIds(AdvertSearchFieldEnum.VATStatus),
        // Only include traditional range IDs if NOT using custom ranges
        mileageIds: !mileageFilter?.useCustomRange ? this.getMinMax(AdvertSearchFieldEnum.Mileage) : undefined,
        capacityIds: !capacityFilter?.useCustomRange ? this.getMinMax(AdvertSearchFieldEnum.Capacity) : undefined,
        priceIds: !priceFilter?.useCustomRange ? this.getMinMax(AdvertSearchFieldEnum.Price) : undefined,
        colourIds: this.getSelectedIds(AdvertSearchFieldEnum.Colour),
        customerIds: this.getSelectedIds(AdvertSearchFieldEnum.Vendor),
        saleTypeId: this.SearchOptions.saleFilter,
        searchSortingEnum: this.SearchOptions.sorting,
        sortingFilterEnum: this.SearchOptions.sortingFilter,
        runner: this.SearchOptions.runner,
        catStatus: this.SearchOptions.catStatus,
        page: this.SearchOptions.page,

        // Enhanced range support - conditionally include custom range properties
        ...(priceFilter?.useCustomRange ? {
          priceMin: priceFilter.minValue,
          priceMax: priceFilter.maxValue
        } : {}),
        ...(mileageFilter?.useCustomRange ? {
          mileageMin: mileageFilter.minValue,
          mileageMax: mileageFilter.maxValue
        } : {}),
        ...(capacityFilter?.useCustomRange ? {
          capacityMin: capacityFilter.minValue,
          capacityMax: capacityFilter.maxValue
        } : {}),
      }
    };

    // mutate existing dto and remove null and empty properties
    Object.keys(filterDTO.filters).forEach((k) => {
      const value = filterDTO.filters[k];
      if (value == null || value === undefined) {
        delete filterDTO.filters[k];
      } else if (Array.isArray(value) && value.length === 0) {
        delete filterDTO.filters[k];
      }
    });

    this.CurrentSearchDTO = filterDTO;
    this.setDisabledFilterState();

    console.log("Current filter: ", this.CurrentSearchDTO);

    return filterDTO;
  }

  async SetFilterListByRole(user: User) {
    const list = this.getFilterList();

    // set 'hidden' flag on filters based on user roles
    list.forEach((x, index, object) => {
      if (x.roles && !this.authService.HasRole(user, x.roles)) {
        object[index].hidden = true;
      }
    });

    this.filterList = list;
  }

  getFilterList(): FilterListItem[] {
    return [
      {
        filterName: 'Vehicle Type',
        searchField: AdvertSearchFieldEnum.VehicleType,
        staticList: true,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true,
        hidden: true
      },
      {
        filterName: 'Make',
        searchField: AdvertSearchFieldEnum.Make,
        staticList: true,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Model',
        searchField: AdvertSearchFieldEnum.Model,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: false
      },
      {
        filterName: 'Derivative',
        searchField: AdvertSearchFieldEnum.Deriv,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: false
      },
      {
        filterName: 'Fuel Type',
        searchField: AdvertSearchFieldEnum.Fuel,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Transmission',
        searchField: AdvertSearchFieldEnum.Transmission,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Body Type',
        searchField: AdvertSearchFieldEnum.BodyType,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Mileage',
        searchField: AdvertSearchFieldEnum.Mileage,
        filterType: SearchFilterTypeEnum.Range,
        items: Array<AdvertSearchCount>(),
        valueType: AdvertFilterValueTypeEnum.Decimal,
        isLookup: true,
        enabled: true
      },
      {
        filterName: 'Plate',
        searchField: AdvertSearchFieldEnum.Plate,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Age',
        searchField: AdvertSearchFieldEnum.Age,
        filterType: SearchFilterTypeEnum.Range,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Engine Capacity',
        searchField: AdvertSearchFieldEnum.Capacity,
        filterType: SearchFilterTypeEnum.Range,
        items: Array<AdvertSearchCount>(),
        isLookup: true,
        enabled: true
      },
      {
        filterName: 'Doors',
        searchField: AdvertSearchFieldEnum.Doors,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'VAT Status',
        searchField: AdvertSearchFieldEnum.VATStatus,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Guide Price',
        searchField: AdvertSearchFieldEnum.Price,
        filterType: SearchFilterTypeEnum.Range,
        items: Array<AdvertSearchCount>(),
        valueType: AdvertFilterValueTypeEnum.Currency,
        isLookup: true, enabled: true
      },
      {
        filterName: 'Colour',
        searchField: AdvertSearchFieldEnum.Colour,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true
      },
      {
        filterName: 'Vendor',
        searchField: AdvertSearchFieldEnum.Vendor,
        filterType: SearchFilterTypeEnum.Checkbox,
        items: Array<AdvertSearchCount>(),
        enabled: true,
        roles: [UserRolesEnum.Admin, UserRolesEnum.AuctionAdmin]
      },
      {
        filterName: 'Cat S/C/D/N',
        searchField: AdvertSearchFieldEnum.CatStatus,
        filterType: SearchFilterTypeEnum.CatStatus,
        items: [],
        enabled: true
      }
      ];
  }

  setDisabledFilterState() {
    // if we have no models selected, disable the deriv filter
    const model = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Model);
    model.enabled = this.CurrentSearchDTO.filters.makeIds && this.CurrentSearchDTO.filters.makeIds.length > 0;

    // if we have no makes selected, disable the model and deriv filters
    const deriv = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Deriv);
    deriv.enabled = model.enabled && this.CurrentSearchDTO.filters.modelIds && this.CurrentSearchDTO.filters.modelIds.length > 0;
  }

  // min max can be either Ids or literal values (i.e. for age they are specific values, for ranges they are Ids)
  getMinMax(searchField: AdvertSearchFieldEnum) {
    const fl = this.filterList.find(x => x.searchField === searchField);

    // Use custom range values if available and we're in custom range mode
    if (fl.useCustomRange && (fl.minValue != null || fl.maxValue != null)) {
      return [fl.minValue || 0, fl.maxValue || 0];
    }

    // Use traditional range IDs only if they have actual non-null values
    if (fl.min != null || fl.max != null) {
      return [fl.min || 0, fl.max || 0];
    }

    // Return empty array when no range is selected (both min and max are null)
    return [];
  }

  getSelectedIds(searchField: AdvertSearchFieldEnum): any[] {
    const fl = this.filterList.find(x => x.searchField === searchField);
    return fl.items ? fl.items.filter(x => x.checked).map(x => x.entityId) : [];
  }

  // Convert age range indices to months for API
  private getAgeRangeInMonths(): number[] {
    const ageRange = this.getMinMax(AdvertSearchFieldEnum.Age);

    if (ageRange.length === 0) {
      return [];
    }

    // Check if values are already in months (like 84) or indices (like 9)
    // If the value is > 13, it's likely already in months
    const minVal = ageRange[0];
    const maxVal = ageRange[1];

    const minMonths = minVal > 13 ? minVal : (minVal != null && minVal !== 0 ? this.convertAgeIndexToMonths(minVal) : 0);
    const maxMonths = maxVal > 13 ? maxVal : (maxVal != null && maxVal !== 0 ? this.convertAgeIndexToMonths(maxVal) : 0);

    console.log("*** Converting age range:", ageRange, "to months:", [minMonths, maxMonths]);

    return [minMonths, maxMonths];
  }

  // Convert age range index to months (matches the dropdown structure you showed)
  private convertAgeIndexToMonths(index: number): number {
    const indexToMonthsMap: { [key: number]: number } = {
      0: 0,    // Any
      1: 6,    // Up to 6 months
      2: 12,   // Up to 1 year
      3: 18,   // Up to 18 months
      4: 24,   // Up to 2 years
      5: 36,   // Up to 3 years
      6: 48,   // Up to 4 years
      7: 60,   // Up to 5 years
      8: 72,   // Up to 6 years
      9: 84,   // Up to 7 years
      10: 96,  // Up to 8 years
      11: 108, // Up to 9 years
      12: 120, // Up to 10 years
      13: 999  // Over 10 years
    };

    return indexToMonthsMap[index] || 0;
  }

  public ResetKeyword() {
    // if we use null the url does not reset properly and retains the keyword
    this.Keywords = '';
  }

  public PopulateFilters(searchCounts: AdvertSearchCountList) {
    this.filterList.forEach(filter => {
      filter.items = searchCounts[AdvertSearchFieldEnum[filter.searchField]];
    });

    let cs = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.CatStatus);
    if (cs) {
      cs.radioItems = this.advertSearchService.catStatusOptions;
    }

    // set the checked items from the current DTO
    if (this.CurrentSearchDTO) {
      // todo: make this dynamic (so adding new filters doesn't require modifying this code etc.)
      this.setItemsState(AdvertSearchFieldEnum.VehicleType, this.CurrentSearchDTO.filters.vehicleTypeIds);
      this.setItemsState(AdvertSearchFieldEnum.Make, this.CurrentSearchDTO.filters.makeIds);
      this.setItemsState(AdvertSearchFieldEnum.Model, this.CurrentSearchDTO.filters.modelIds);
      this.setItemsState(AdvertSearchFieldEnum.Deriv, this.CurrentSearchDTO.filters.derivativeIds);
      this.setItemsState(AdvertSearchFieldEnum.Fuel, this.CurrentSearchDTO.filters.fuelTypeIds);
      this.setItemsState(AdvertSearchFieldEnum.Transmission, this.CurrentSearchDTO.filters.transmissionTypeIds);
      this.setItemsState(AdvertSearchFieldEnum.BodyType, this.CurrentSearchDTO.filters.bodyTypeIds);
      this.setItemsState(AdvertSearchFieldEnum.Mileage, this.CurrentSearchDTO.filters.mileageIds);
      this.setItemsState(AdvertSearchFieldEnum.Age, this.CurrentSearchDTO.filters.ageRange);
      this.setItemsState(AdvertSearchFieldEnum.Capacity, this.CurrentSearchDTO.filters.capacityIds);
      this.setItemsState(AdvertSearchFieldEnum.Doors, this.CurrentSearchDTO.filters.doors);
      this.setItemsState(AdvertSearchFieldEnum.Plate, this.CurrentSearchDTO.filters.plateIds);
      this.setItemsState(AdvertSearchFieldEnum.Price, this.CurrentSearchDTO.filters.priceIds);
      this.setItemsState(AdvertSearchFieldEnum.VATStatus, this.CurrentSearchDTO.filters.vatStatusIds);
      this.setItemsState(AdvertSearchFieldEnum.Colour, this.CurrentSearchDTO.filters.colourIds);
      this.setItemsState(AdvertSearchFieldEnum.Vendor, this.CurrentSearchDTO.filters.customerIds);

      // set search options
      this.SearchOptions.saleFilter = this.CurrentSearchDTO.filters.saleTypeId;
      this.SearchOptions.sortingFilter = this.CurrentSearchDTO.filters.sortingFilterEnum;
      this.SearchOptions.sorting = this.CurrentSearchDTO.filters.searchSortingEnum;
      this.SearchOptions.runner = this.CurrentSearchDTO.filters.runner;
      this.SearchOptions.catStatus = this.CurrentSearchDTO.filters.catStatus;

      // Handle custom range values from URL
      this.setCustomRangeFromDTO(AdvertSearchFieldEnum.Price, this.CurrentSearchDTO.filters.priceMin, this.CurrentSearchDTO.filters.priceMax);
      this.setCustomRangeFromDTO(AdvertSearchFieldEnum.Mileage, this.CurrentSearchDTO.filters.mileageMin, this.CurrentSearchDTO.filters.mileageMax);
      this.setCustomRangeFromDTO(AdvertSearchFieldEnum.Capacity, this.CurrentSearchDTO.filters.capacityMin, this.CurrentSearchDTO.filters.capacityMax);
      this.SearchOptions.page = this.CurrentSearchDTO.filters.page;
    }

    this.setDisabledFilterState();

    this.logger.log("Populated filters: ", this.filterList);
  }

  public EnsureValidSearchDTO(searchDTO: AdvertSearchDTO) {
    this.logger.log("Ensuring valid search dto: ", searchDTO);

    searchDTO.filters.saleTypeId = this.SearchOptions.saleFilter;
    searchDTO.filters.searchSortingEnum = +this.SearchOptions.sorting || SortingType.NewlyListed;
    searchDTO.filters.sortingFilterEnum = +this.SearchOptions.sortingFilter || SortingFilter.AnyTime;
    searchDTO.filters.runner = this.SearchOptions.runner;
    searchDTO.filters.catStatus = this.SearchOptions.catStatus;
    searchDTO.filters.keywords = this.Keywords;

    // todo: make this dynamic
    searchDTO.filters.vehicleTypeIds = this.ensureArray(searchDTO.filters.vehicleTypeIds, AdvertSearchFieldEnum.VehicleType);
    searchDTO.filters.makeIds = this.ensureArray(searchDTO.filters.makeIds, AdvertSearchFieldEnum.Make);
    searchDTO.filters.modelIds = this.ensureArray(searchDTO.filters.modelIds, AdvertSearchFieldEnum.Model);
    searchDTO.filters.derivativeIds = this.ensureArray(searchDTO.filters.derivativeIds, AdvertSearchFieldEnum.Deriv);
    searchDTO.filters.fuelTypeIds = this.ensureArray(searchDTO.filters.fuelTypeIds, AdvertSearchFieldEnum.Fuel);
    searchDTO.filters.transmissionTypeIds = this.ensureArray(searchDTO.filters.transmissionTypeIds, AdvertSearchFieldEnum.Transmission);
    searchDTO.filters.bodyTypeIds = this.ensureArray(searchDTO.filters.bodyTypeIds, AdvertSearchFieldEnum.BodyType);
    searchDTO.filters.mileageIds = this.ensureArray(searchDTO.filters.mileageIds, AdvertSearchFieldEnum.Mileage);
    searchDTO.filters.ageRange = this.ensureArray(searchDTO.filters.ageRange, AdvertSearchFieldEnum.Age);
    searchDTO.filters.capacityIds = this.ensureArray(searchDTO.filters.capacityIds, AdvertSearchFieldEnum.Capacity);
    searchDTO.filters.doors = this.ensureArray(searchDTO.filters.doors, AdvertSearchFieldEnum.Doors);
    searchDTO.filters.plateIds = this.ensureArray(searchDTO.filters.plateIds, AdvertSearchFieldEnum.Plate);
    searchDTO.filters.priceIds = this.ensureArray(searchDTO.filters.priceIds, AdvertSearchFieldEnum.Price);
    searchDTO.filters.vatStatusIds = this.ensureArray(searchDTO.filters.vatStatusIds, AdvertSearchFieldEnum.VATStatus);
    searchDTO.filters.colourIds = this.ensureArray(searchDTO.filters.colourIds, AdvertSearchFieldEnum.Colour);
    searchDTO.filters.customerIds = this.ensureArray(searchDTO.filters.customerIds, AdvertSearchFieldEnum.Vendor);
  }

  private ensureArray(data, searchField: AdvertSearchFieldEnum) {
    if (!data || data.length === 0) {
      return data;
    }

    return Array.isArray(data) ? data : [data];
  }

  private setItemsState(searchField: AdvertSearchFieldEnum, idData) {
    const item = this.filterList.find(fl => fl.searchField === searchField);

    item.min = null;
    item.max = null;

    if (!idData || idData.length === 0 || !item.items) {
      return;
    }

    idData = Array.isArray(idData) ? idData : [idData];

    if (item.filterType === SearchFilterTypeEnum.Range) {
      item.min = +idData[0];
      item.max = idData.length > 1 ? +idData[1] : null;
      return;
    }

    // clone the items in the filter list
    idData.forEach(x => {
      const thisItem = item.items.filter(y => y.entityId == x);
      if (!thisItem || thisItem.length === 0) {
        this.logger.error("Couldn't find corresponding entity from query, field: ",
          AdvertSearchFieldEnum[item.searchField], " - entityId: ", x);
        return;
      }

      if (item.filterType === SearchFilterTypeEnum.Checkbox) {
        thisItem[0].checked = true;
      }
    });
  }

  // true if filter has checked items or a range selected
  public FilterHasSelections(filterItem: FilterListItem) {
    if (filterItem.filterType === SearchFilterTypeEnum.Checkbox) {
      return filterItem.items && filterItem.items.filter(x => x.checked).length > 0;
    }

    return !!(filterItem.min || filterItem.max || filterItem.minValue || filterItem.maxValue);
  }

  public ResetRangeFilter(filterItem: FilterListItem) {
    filterItem.min = null;
    filterItem.max = null;
    filterItem.minValue = null;
    filterItem.maxValue = null;
    filterItem.useCustomRange = false;
  }

  public ResetItem(item: AdvertSearchCount) {
    item.checked = false;
  }

  public ResetFilter(filter: FilterListItem) {
    if (filter.filterType === SearchFilterTypeEnum.Keyword) {
      this.ResetKeyword();
      return;
    }

    if (filter.filterType === SearchFilterTypeEnum.Checkbox) {
      if (filter.items) {
        filter.items.forEach(x => {
          this.ResetItem(x);
        });

        // if this is the Make filter, clear Model and Deriv filters
        if (filter.searchField === AdvertSearchFieldEnum.Make) {
          this.clearFilter(AdvertSearchFieldEnum.Model);
          this.clearFilter(AdvertSearchFieldEnum.Deriv);
        }

        // if it's the Model filter, clear deriv filters
        if (filter.searchField === AdvertSearchFieldEnum.Model) {
          this.clearFilter(AdvertSearchFieldEnum.Deriv);
        }
      }
    } else {
      this.ResetRangeFilter(filter);
    }
  }

  private clearFilter(searchField: AdvertSearchFieldEnum) {
    const filter = this.getFilter(searchField);
    if (filter != null) {
      this.ResetFilter(filter);
    }
  }

  private getFilter(searchField: AdvertSearchFieldEnum) {
    const result = this.filterList.filter(x => x.searchField === searchField);
    if (result) {
      return result[0];
    } else {
      return null;
    }
  }

  public ClearAllFilters() {
    this.SearchOptions.page = 1;
    this.filterList.forEach(filter => {
      this.ResetFilter(filter);
    });
  }

  public GetSelectedCount(filterItem: FilterListItem) {
    if (filterItem.filterType === SearchFilterTypeEnum.Checkbox) {
      return filterItem.items.filter(x => x.checked).length;
    } else {
      return -1; // not applicable for range filter
    }
  }

  // true if any filters are selected
  public SearchIsDefined(): boolean {
    let isDefined = false;

    this.filterList.every(filter => {
      if (this.FilterHasSelections(filter)) {
        isDefined = true;
        return false;
      }

      return true;
    });

    // 'runner' not being set to 'All' also counts as a filter
    if (this.CurrentSearchDTO.filters.runner >= 0) {
      return true;
    }

    if (this.CurrentSearchDTO.filters.catStatus >= 0) {
      return true;
    }

    return isDefined;
  }

  public SearchProfileIsDefined(): boolean {
    const vendorFilter = this.getFilter(AdvertSearchFieldEnum.Vendor);
    return (vendorFilter != null && this.FilterHasSelections(vendorFilter));
  }

  // creates an object describing all the current filter selections
  public GetSearchDescriptionObject() {
    const searchDesc = [];

    this.filterList.forEach(filter => {
      if (filter.filterType === SearchFilterTypeEnum.Checkbox) {
        if (filter.items) {
          const count = filter.items.reduce((n, x) => n + (x.checked ? 1 : 0), 0);
          if (count > 0) {
            const desc = filter.items.filter(x => x.checked).map(x => x.name).join(', ');
            searchDesc.push({name: filter.filterName, description: desc});
          }
        }
      } else if (filter.filterType === SearchFilterTypeEnum.Range) {
        if (filter.min || filter.max) {
          searchDesc.push({
            name: filter.filterName,
            description: this.advertSearchService.getRangeValuesString(filter.searchField, filter.min, filter.max)
          });
        }
      }
    });

    if (this.Keywords && this.Keywords.trim() != '') {
      searchDesc.push({name: 'Keywords', description: this.Keywords});
    }

    // 'runner' not being set to 'All' also counts as a filter
    if (this.CurrentSearchDTO.filters.runner >= 0) {
      searchDesc.push({
        name: "Runner",
        description: this.advertSearchService.getRunnerFilterString(this.CurrentSearchDTO.filters.runner)
      });
    }

    if (this.CurrentSearchDTO.filters.catStatus >= 0) {
      searchDesc.push({
        name: "Cat Status",
        description: this.advertSearchService.getCatStatusFilterString(this.CurrentSearchDTO.filters.catStatus)
      });
    }

    return searchDesc;
  }

  // Enhanced range methods for improved UX
  public SetCustomRangeValue(filterItem: FilterListItem, minValue?: number, maxValue?: number) {
    if (minValue === undefined && maxValue === undefined) {
      // This is a clear operation
      filterItem.useCustomRange = false;
      filterItem.minValue = null;
      filterItem.maxValue = null;
      filterItem.min = null;
      filterItem.max = null;
    } else {
      filterItem.useCustomRange = true;
      filterItem.minValue = minValue;
      filterItem.maxValue = maxValue;
      // Clear range IDs when using custom values to avoid conflicts
      filterItem.min = null;
      filterItem.max = null;
    }
  }

  public SetQuickFilter(filterItem: FilterListItem, quickFilter: { label: string, min?: number, max?: number }) {
    if (quickFilter.label === 'Clear') {
      // Clear all range selections
      this.ResetRangeFilter(filterItem);
    } else {
      this.SetCustomRangeValue(filterItem, quickFilter.min, quickFilter.max);
    }
  }

  public InitializeQuickFilters() {
    // Set up quick filters for common ranges
    const priceFilter = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Price);
    if (priceFilter) {
      priceFilter.quickFilters = [
        { label: 'Under £5k', min: undefined, max: 5000 },
        { label: '£5k-£10k', min: 5000, max: 10000 },
        { label: '£10k-£15k', min: 10000, max: 15000 },
        { label: '£15k-£25k', min: 15000, max: 25000 },
        { label: '£25k+', min: 25000, max: undefined },
        { label: 'Clear', min: undefined, max: undefined }, // Clear option
      ];
    }

    const mileageFilter = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Mileage);
    if (mileageFilter) {
      mileageFilter.quickFilters = [
        { label: 'Under 25k', min: undefined, max: 25000 },
        { label: '25k-50k', min: 25000, max: 50000 },
        { label: '50k-75k', min: 50000, max: 75000 },
        { label: '75k-100k', min: 75000, max: 100000 },
        { label: 'Over 100k', min: 100000, max: undefined },
        { label: 'Clear', min: undefined, max: undefined }, // Clear option
      ];
    }

    const capacityFilter = this.filterList.find(x => x.searchField === AdvertSearchFieldEnum.Capacity);
    if (capacityFilter) {
      capacityFilter.quickFilters = [
        { label: 'Under 1.0L', min: undefined, max: 1000 },
        { label: '1.0L-1.5L', min: 1000, max: 1500 },
        { label: '1.5L-2.0L', min: 1500, max: 2000 },
        { label: '2.0L-3.0L', min: 2000, max: 3000 },
        { label: 'Over 3.0L', min: 3000, max: undefined },
        { label: 'Clear', min: undefined, max: undefined }, // Clear option
      ];
    }
  }

  public GetRangeDisplayString(filterItem: FilterListItem): string {
    // Check if we have custom range values
    if (filterItem.useCustomRange && (filterItem.minValue || filterItem.maxValue)) {
      const customDisplay = this.getCustomRangeDisplayString(filterItem);
      if (customDisplay) return customDisplay;
    }

    // Fall back to traditional range display
    const traditionalDisplay = this.advertSearchService.getRangeValuesString(
      filterItem.searchField,
      filterItem.min,
      filterItem.max
    );

    // Return traditional display or empty string if no range is selected
    return traditionalDisplay || '';
  }

  private getCustomRangeDisplayString(filterItem: FilterListItem): string {
    const minVal = filterItem.minValue;
    const maxVal = filterItem.maxValue;

    let transform: (val: number) => string;
    switch (filterItem.searchField) {
      case AdvertSearchFieldEnum.Price:
        transform = this.advertSearchService.transformPrice.bind(this.advertSearchService);
        break;
      case AdvertSearchFieldEnum.Mileage:
        transform = this.advertSearchService.transformMileage.bind(this.advertSearchService);
        break;
      case AdvertSearchFieldEnum.Capacity:
        transform = this.advertSearchService.transformCapacity.bind(this.advertSearchService);
        break;
      default:
        transform = (val) => val?.toString() || '';
        break;
    }

    if (!minVal && maxVal) {
      return `Up to ${transform(maxVal)}`;
    } else if (minVal && !maxVal) {
      return `From ${transform(minVal)}`;
    } else if (minVal && maxVal) {
      return `${transform(minVal)} - ${transform(maxVal)}`;
    }
    return '';
  }

  private setCustomRangeFromDTO(searchField: AdvertSearchFieldEnum, minValue?: number, maxValue?: number) {
    if (minValue != null || maxValue != null) {
      const filter = this.filterList.find(x => x.searchField === searchField);
      if (filter) {
        filter.useCustomRange = true;
        filter.minValue = minValue;
        filter.maxValue = maxValue;
        // Clear traditional range IDs to avoid conflicts
        filter.min = null;
        filter.max = null;
      }
    }
  }
}
