import {FilterListItem} from '../../../../services/models/filterListitem';
import {deepClone} from 'fast-json-patch';
import {AdvertSearchService} from '../../../../services';
import {LoggerService} from "../../../../global/services";
import {AdvertFilterValueTypeEnum, AdvertSearchFieldEnum, SearchFilterTypeEnum} from "../../../../global/enums";
import {} from "../../../../global/interfaces";

export class AdvertSearchLookups {
  constructor(private advertSearchService: AdvertSearchService,
              private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  public ResetAllDropdowns(filterList: FilterListItem[]) {
    filterList.forEach(filter => {
      if (filter.filterType === SearchFilterTypeEnum.Range) {
        this.ResetRangeDropdown(filter);
      }
    });
  }

  public ResetRangeDropdown(filterItem: FilterListItem) {
    //this.logger.log("Resetting range dropdown: ", filterItem);

    if (filterItem.isLookup) {
      this.populateLookupDropdown(filterItem);
      return;
    }

    filterItem.dropdownItemsMin = [{value: 0, label: 'Any'}];
    filterItem.dropdownItemsMax = [{value: 0, label: 'Any'}];
    filterItem.items.forEach(item => {
      let label;

      switch (filterItem.valueType) {
        case AdvertFilterValueTypeEnum.Currency:
          label = this.advertSearchService.currencyPipe.transform(item.name);
          break;
        case AdvertFilterValueTypeEnum.Date:
          label = this.advertSearchService.datePipe.transform(item.name);
          break;
        case AdvertFilterValueTypeEnum.Decimal:
          label = this.advertSearchService.decimalPipe.transform(item.name);
          break;
        default:
          // Special handling for Age filter to remove "up to" prefix
          if (filterItem.searchField === AdvertSearchFieldEnum.Age) {
            label = item.name.replace(/^up to\s*/i, '');
          } else {
            label = item.name;
          }
          break;
      }

      let count = this.getDropdownItemCount(filterItem, item.entityId);

      filterItem.dropdownItemsMin.push({
        value: item.entityId, label: label + ' (' + count + ')'
      });
      if (!filterItem.min || filterItem.min < item.entityId) {
        filterItem.dropdownItemsMax.push({
          value: item.entityId, label: label + ' (' + count + ')'
        });
      }
    });

    //this.logger.log("Filtering min/max for: ", filterItem);

    // remove max items below current min (if set)
    if (filterItem.min) {
      filterItem.dropdownItemsMax = filterItem.dropdownItemsMax.filter(x => x.value > filterItem.min);
    }

    // remove min items above current max (if set)
    if (filterItem.max) {
      filterItem.dropdownItemsMin = filterItem.dropdownItemsMin.filter(x => x.value < filterItem.max);
    }

    filterItem.dropdownItemsMin.sort((a, b) => {
      return a.value - b.value;
    });
    filterItem.dropdownItemsMax.sort((a, b) => {
      return a.value - b.value;
    });
  }

  populateLookupDropdown(filterItem: FilterListItem) {
    let filtered = this.getRangeLookup(filterItem.searchField);

    //this.logger.log("FILTERED: ",  filtered);

    let filteredMin = deepClone(filtered);
    let filteredMax = deepClone(filtered);

    //filtered = filtered.filter(x => filterItem.items.map(y => y.entityId).includes(x.id));

    // filter out prices below current min
    if (filterItem.min) {
      let minCost = filteredMax.filter(y => y.id == filterItem.min);
      if (minCost && minCost.length > 0) {
        filteredMax = filteredMax.filter(x => x.max > minCost[0].min);
      }
    }

    // filter out values above current max
    if (filterItem.max) {
      let maxCost = filteredMin.filter(y => y.id == filterItem.max);
      if (maxCost && maxCost.length > 0) {
        filteredMin = filteredMin.filter(x => x.min < maxCost[0].max);
      }
    }

    switch (filterItem.searchField) {
      case AdvertSearchFieldEnum.Price:
        filterItem.dropdownItemsMin = filteredMin.map(x => {
            let count = this.getDropdownItemCount(filterItem, x.id);
            return {label: this.advertSearchService.transformPrice(x.min) + ' (' + count + ')', value: x.id};
          }
        );

        filterItem.dropdownItemsMax = filteredMax.map(x => {
            let count = this.getDropdownItemCount(filterItem, x.id);
            return {label: this.advertSearchService.transformPrice(x.max) + ' (' + count + ')', value: x.id};
          }
        );
        break;
      case AdvertSearchFieldEnum.Mileage:
        filterItem.dropdownItemsMin = filteredMin.map(x => {
            let count = this.getDropdownItemCount(filterItem, x.id);
            return {label: this.advertSearchService.transformMileage(x.min) + ' (' + count + ')', value: x.id};
          }
        );

        filterItem.dropdownItemsMax = filteredMax.map(x => {
            let count = this.getDropdownItemCount(filterItem, x.id);
            return {label: this.advertSearchService.transformMileage(x.max) + ' (' + count + ')', value: x.id};
          }
        );
        break;
      case AdvertSearchFieldEnum.Capacity:
        filterItem.dropdownItemsMin = filteredMin.map(x => {
            let count = this.getDropdownItemCount(filterItem, x.id);
            return {label: this.advertSearchService.transformCapacity(x.min) + ' (' + count + ')', value: x.id};
          }
        );

        filterItem.dropdownItemsMax = filteredMax.map(x => {
            let count = this.getDropdownItemCount(filterItem, x.id);
            return {label: this.advertSearchService.transformCapacity(x.max) + ' (' + count + ')', value: x.id};
          }
        );
        break;
    }
  }

  private getDropdownItemCount(filterItem: FilterListItem, id: number) {
    //this.logger.log("Getting dropdown count for ", filterItem, " - id: ", id)

    let item = filterItem.items.filter(y => y.entityId == id);
    let count = 0;
    if (item && item.length > 0) {
      count = item[0].count;
    }
    return count;
  }

  getRangeLookup(searchField: AdvertSearchFieldEnum) {
    let filtered: any;
    switch (searchField) {
      case AdvertSearchFieldEnum.Price:
        filtered = this.advertSearchService.Lookups.priceRanges;
        break;
      case AdvertSearchFieldEnum.Capacity:
        filtered = this.advertSearchService.Lookups.capacityRanges;
        break;
      case AdvertSearchFieldEnum.Mileage:
        filtered = this.advertSearchService.Lookups.mileageRanges;
        break;
    }

    return filtered;
  }
}
