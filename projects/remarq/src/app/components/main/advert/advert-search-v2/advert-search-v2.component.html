<header id="advert-search-nav">

  <!-- Side bar filter list -->
  <mdb-side-nav #sidenav [fixed]="true" [sidenavBreakpoint]="1200" (close)="sideNavClosed($event)">

    <!--
    <div class="text-center mt-3" *ngIf="isLoading" style="color: var(--textColour)">
      <i class="fa fa-spinner fa-spin fa-2x"></i>
      <h1 class="mt-2">Loading</h1>
    </div>
    -->

    <div #navLinks>

      <div *ngIf="SearchFilters.Keywords">
        <ul>
          <li class="nav-item"><a class="nav-link"><span class="nav-label">Keywords</span></a></li>
        </ul>
        <div class="selected-items-container">
          <div class="chosen-items">
            &bull; {{ SearchFilters.Keywords }}
            <div class="reset-filter-inline">
                <span (click)="resetKeywordFilter()">
                  <i class="fa fa-times-circle"></i>
                </span>
            </div>
          </div>
        </div>
      </div>

      <links>
        <div *ngFor="let filterItem of SearchFilters.filterList">
          <ul>
            <li *ngIf="!filterItem.hidden" class="nav-item" id="filterItem-{{ filterItem.filterName }}"
                [ngClass]="{'disabled': !filterItem.enabled}">
              <a class="nav-link" [ngClass]="{'disabled': !filterItem.enabled}" id="filterItem.filter_label"
                 (click)="showFilterFlyout(filterItem)">
                <span class="nav-label">{{ filterItem.filterName }}</span>
                <span class="nav-caret"><i class="fa fa-angle-right"></i></span>
              </a>
              <div class="selected-items-container">

                <div class="chosen-items">

                  <div *ngIf="filterItem.filterType === SearchFilterType.Checkbox && filterItem.items?.length > 0">
                    <div *ngFor="let item of filterItem.items; last as isLast">

                      <span class="selected-item {{ filterItem.filterName | nospaces | lowercase }}"
                            *ngIf="item.checked" (click)="uncheckItem(item)"> &bull; {{ item.name }}
                        <span *ngIf="item.checked" class="reset-filter-inline"><i class="fa fa-times-circle"></i></span>
                      </span>

                    </div>
                  </div>

                  <div *ngIf="filterItem.filterType === SearchFilterType.CatStatus">
                    <span
                      class="selected-item">{{ advertSearchService.getCatStatusFilterString(SearchFilters.SearchOptions.catStatus) }}
                    </span>
                  </div>

                  <!--
                  <div *ngIf="!filterItem.enabled">
                    <span *ngIf="filterItem.searchField == advertSearchField.Model">Select Make(s)</span>
                    <span *ngIf="filterItem.searchField == advertSearchField.Deriv">Select Model(s)</span>
                  </div>
                  -->

                  <span
                    *ngIf="filterItem.filterType === SearchFilterType.Range && SearchFilters.FilterHasSelections(filterItem)">
                    <span
                      class="selected-item">{{ getRangeDisplayValue(filterItem) }}</span>
                  </span>

                  <span class="reset-filter-inline" *ngIf="filterItem.filterType == SearchFilterType.Range">
                    <span *ngIf="SearchFilters.FilterHasSelections(filterItem)" (click)="resetRangeFilter(filterItem)"
                          class="selected-item">
                      <i class="fa fa-times-circle"></i>
                    </span>
                  </span>
                </div>

              </div>
            </li>
          </ul>
        </div>
      </links>
    </div>


    <div class="flex-shrink-1" style="text-align: center; margin-top: 10px;">

      <div class="btn-group select-sale-type">
        <label *ngFor="let option of advertSearchService.runnerOptions"
               class="btn btn-sm"
               [value]="option.value"
               [class.active]="option.value == SearchFilters.SearchOptions.runner"
               mdbRadio="{{ option.value }}"
               [(ngModel)]="SearchFilters.SearchOptions.runner"
               (click)="runnerFilterSelected()">
          {{ option.label }}
        </label>
      </div>
    </div>


    <div class="view-results mt-2">
      <div class="text-center clear-filters">
        <button class="btn ungrouped btn-secondary" (click)="clearAllFilters()">
          Clear all filters
        </button>
      </div>
      <div class="text-center clear-filters">
        <button class="btn ungrouped btn-secondary" (click)="showSaveSearchDialog()">
          Save Search
        </button>
      </div>
      <div class="text-center clear-filters" *ngIf="user && user.isAdmin">
        <button class="btn ungrouped btn-secondary" (click)="showSaveSearchProfileDialog()">
          Save Profile
        </button>
      </div>
    </div>

  </mdb-side-nav>

  <!-- Start of Filter popout -->
  <div
    ignoreOutsideClasses="nav-label,mdb-select-dropdown,mdb-select-option,mdb-option,select-dropdown,dropdown-content,nav-link,nav-caret,fa-angle-right,mdb-select-wrapper,mdb-select-filter-input"
    (clickOutside)="flyoutClosed()" id="filterWrapper">
    <!-- list all items for the currently selected filterItem -->

    <!--    <div *ngIf="selectedFilter && selectedFilter.selectedItems"-->
    <!--         #flyOut class="filterValues" [style]="{'top':selectedFilterTop}">-->
    <div *ngIf="selectedFilter && (selectedFilter.items || selectedFilter.radioItems)"
         #flyOut class="filterValues">

      <div class="filter-items-container">
        <!-- CHECKBOX type popout -->
        <div *ngIf="selectedFilter.filterType === SearchFilterType.Checkbox">
          <div *ngFor="let item of selectedFilter?.items;">
            <div class="group-header" *ngIf="item.groupName">
              {{ item.groupName }}
            </div>

            <div class="form-check form-check-inline pl-1">

              <!-- disable checkboxes if we have max filters selected for make -->
              <mdb-checkbox [(ngModel)]="item.checked"
                            id="filter-{{ item.entityId}}"
                            [disabled]="selectedFilter.searchField == SearchField.Make && SearchFilters.GetSelectedCount(selectedFilter) == 5 && !item.checked"></mdb-checkbox>

              <label class="form-check-label" [attr.for]="'filter-' + item.entityId">
                <span class="filterLabel">{{ item.name }}</span>
                <span class="filterCount">({{ item.count }})</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- RANGE type popout -->
      <div *ngIf="selectedFilter.filterType === SearchFilterType.Range">

        <!-- Enhanced Range Controls -->
        <div class="enhanced-range-container">

          <!-- Quick Filter Pills -->
          <div class="quick-filters mb-3" *ngIf="selectedFilter.quickFilters?.length > 0">
            <div class="quick-filters-label">Quick filters:</div>
            <div class="d-flex flex-wrap" style="grid-gap: 5px;">
              <button *ngFor="let quickFilter of selectedFilter.quickFilters; let i = index"
                      class="btn btn-sm quick-filter-pill"
                      [class.btn-outline-primary]="i != (selectedFilter.quickFilters.length - 1)"
                      [class.btn-default]="(i == selectedFilter.quickFilters.length - 1)"
                      (click)="onQuickFilterClick(selectedFilter, quickFilter)">
                {{ quickFilter.label }}
              </button>
            </div>
          </div>


          <!-- Traditional Dropdowns (always show - they are the main interface) -->
          <div class="traditional-range-dropdowns mt-3">
            <div class="row">
              <div class="col-md-6">
                <div class="filter-label">From {{ selectedFilter.filterName }}:</div>
                <div class="select select-sm">
                  <mdb-select-2 [outline]="true"
                                [(ngModel)]="selectedFilter.min"
                                (ngModelChange)="onRangeMinChanged(selectedFilter, $event)"
                                placeholder="Select min">
                    <mdb-select-option *ngFor="let item of selectedFilter.dropdownItemsMin" [value]="item.value">
                      {{ item.label }}
                    </mdb-select-option>
                  </mdb-select-2>
                </div>
              </div>

              <div class="col-md-6">
                <div class="filter-label">To {{ selectedFilter.filterName }}:</div>
                <div class="select select-sm">
                  <mdb-select-2 [outline]="true"
                                [(ngModel)]="selectedFilter.max"
                                (ngModelChange)="onRangeMaxChanged(selectedFilter, $event)"
                                placeholder="Select max">
                    <mdb-select-option *ngFor="let item of selectedFilter.dropdownItemsMax" [value]="item.value">
                      {{ item.label }}
                    </mdb-select-option>
                  </mdb-select-2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CAT S/C/D/N -->

      <div *ngIf="selectedFilter.filterType === SearchFilterType.CatStatus" style="display: inline-grid">
        <div *ngFor="let item of selectedFilter?.radioItems" class="btn-group">
          <label
            class="btn btn-sm"
            [value]="item.value"
            [ngClass]="{ 'active': item.value == SearchFilters.SearchOptions.catStatus }"
            mdbRadio="{{ item.value }}"
            [(ngModel)]="SearchFilters.SearchOptions.catStatus"
            (click)="radioFilterSelected()">
            {{ item.label }}
          </label>
        </div>
      </div>

      <!-- -->

      <!-- Action buttons -->
      <div class="row pt-3">

        <div class="col-6">

          <div class="text-left">
            <span class="btn btn-secondary btn-sm action-button" (click)="onDoneButtonClick()">Done</span>
          </div>
        </div>
        <div class="col-6">
          <div class="text-right">
            <button class="btn btn-sm btn-default action-button" (click)="resetFilter(selectedFilter)">
              <mdb-icon fas icon="undo"></mdb-icon>
              Clear
            </button>
          </div>
        </div>
      </div>

      <!-- status text -->
      <div
        *ngIf="selectedFilter.searchField == SearchField.Make && SearchFilters.GetSelectedCount(selectedFilter) == 5">
        * Maximum number of Makes selected
      </div>

    </div>
  </div>

</header>


<!--Main Layout-->
<main id="advert-search-main-panel">
  <div class="mt-3 mb-3">

    <div *ngIf="SearchFilters?.CurrentSearchDTO?.filters?.customerId != null" class="only-customer">

      Showing only customer:  {{ SearchFilters?.CurrentSearchDTO?.filters?.customerId }}

    </div>

    <div *ngIf="watchlistService.watchlistLoaded">


      <div class="d-flex flex-wrap pt-1 indent-on-mobile grid-gap-10" style="align-items: center;">

        <!-- SALE TYPE -->
        <div class="flex-grow-1 flex-md-shrink-1 flex-md-grow-0 d-flex flex-wrap grid-gap-5">

          <div class="flex-shrink-1">
            <a (click)="sidenav.toggle()"
               class="d-inline-block d-xl-none button-collapse hidden-nav-button-collapse mx-0 mb-0 mr-2">
              <button class="btn btn-sm btn-secondary btn-block">
                <mdb-icon fas icon="bars"></mdb-icon>
                Filter
              </button>
            </a>
          </div>


          <div class="btn-group select-sale-type">
            <label *ngFor="let option of advertSearchService.saleFilters"
                   class="btn btn-sm"
                   [value]="option.value"
                   [ngClass]="{ 'active': option.value == SearchFilters.SearchOptions.saleFilter }"
                   mdbRadio="{{ option.value }}"
                   [(ngModel)]="SearchFilters.SearchOptions.saleFilter"
                   (click)="saleFilterSelected(option)">
              {{ option.label }}
            </label>
          </div>
        </div>

        <!-- RUNNER -->
        <!--        <div class="flex-shrink-1">-->

        <!--          <div class="btn-group select-sale-type" style="margin-left: 15px;">-->
        <!--            <label *ngFor="let option of AdvertSearchService.runnerOptions"-->
        <!--                   class="btn btn-sm"-->
        <!--                   [value]="option.value"-->
        <!--                   [ngClass]="{ 'active': option.value == SearchFilters.SearchOptions.runner }"-->
        <!--                   mdbRadio="{{ option.value }}"-->
        <!--                   [(ngModel)]="SearchFilters.SearchOptions.runner"-->
        <!--                   (click)="runnerFilterSelected()">-->
        <!--              {{option.label}}-->
        <!--            </label>-->
        <!--          </div>-->
        <!--        </div>-->

        <div class="flex-grow-1">
          <div class="ml-2 result-count">{{ getSearchHeader() }}</div>
        </div>

        <div class="d-none d-md-block">

          <div class="d-flex grid-gap-10 flex-wrap">

            <div class="flex-shrink-1 flex-sm-grow-0">
              <button class="btn btn-sm btn-secondary btn-block" (click)="loadSearchResults()"><i
                class="fa fa-redo"></i>
              </button>
            </div>

            <div class="flex-shrink-1 flex-sm-grow-0">
              <button class="btn btn-sm btn-secondary btn-block" (click)="showSaveSearchDialog()">
                Save Search
              </button>
            </div>

            <div class="flex-shrink-1 top-row-select" style="white-space: nowrap">
              <div class="input-group input-group-sm">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    Added
                  </div>
                </div>
                <div style="width: 140px;">
                  <mdb-select [outline]="true"
                              [style]="{'display':'inline-block;'}"
                              [(ngModel)]="SearchFilters.SearchOptions.sortingFilter"
                              [ngModelOptions]=" {standalone: true }"
                              [options]="advertSearchService.sortingFilters" (selected)="sortingFilterOptionChanged()"
                              placeholder="Sorting Filter"></mdb-select>
                </div>
              </div>
            </div>

            <div class="flex-shrink-1 top-row-select">
              <div class="input-group input-group-sm">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    Sort
                  </div>
                </div>
                <div style="display: inline-block; width: 175px;">
                  <mdb-select [outline]="true"
                              [ngModel]="SearchFilters.SearchOptions.sorting"
                              [ngModelOptions]=" {standalone: true }"
                              [options]="advertSearchService.sortingTypes"
                              (ngModelChange)="sortingOptionChanged($event)"
                              placeholder="Select Sorting"></mdb-select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="!results" id="advert-search-waiting-widget">
        <div *ngFor="let x of [1,2,3]" class="ghost">
          <div class="ghost-waiting ghost-result-item widget widget-border">
          </div>
        </div>
      </div>

      <div *ngIf="results && results.totalResults == 0">

        <div class="attention-box p-3 mb-2 mt-3">

          <div class="d-flex flex-wrap">
            <div class="flex-grow-1 flex-md-shrink-1 flex-md-grow-0">
              <div class="px-3 text-center pb-2">
                <img src="/assets/images/svg/sad.svg" class="svg-color" height="90" width="90"
                     style="fill: #f00; color: #f00;">
              </div>
            </div>
            <div class="flex-grow-1 text-center text-md-left">
              <div class="pl-3">
                <div class="pt-2">
                  <h1>There are no results for this search</h1>
                </div>
                <div class="mt-3">
                  <h2>Change your last filter or reset the form</h2>
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>
    </div>
  </div>

  <div *ngIf="results?.advertSearchSummaryAdverts">

    <div *ngFor="let advert of results.advertSearchSummaryAdverts | paginate : {
                itemsPerPage: tableSize,
                currentPage: SearchFilters.SearchOptions.page,
                totalItems: count
              };">
      <app-advert-search-summary [advert]="advert" [user]="user"></app-advert-search-summary>
    </div>

    <div class="row flex-row-reverse mb-3 mt-3">
      <div class="col-sm-6 text-md-right text-center">
        <div class="pt-1 pagination-wrapper">
          <pagination-controls
            class="paginator"
            previousLabel="Prev"
            nextLabel="Next"
            (pageChange)="onTableDataChange($event)">
          </pagination-controls>
        </div>
      </div>

      <div class="col-sm-6 text-md-left text-center pt-3 pt-md-0">
        <div style="display: inline-block">

          <div class="input-group input-group-sm w-auto">
            <div class="input-group-prepend">
              <div class="input-group-text">
                Results Per Page
              </div>
            </div>
            <div class="input-group-item">
              <mdb-select-2
                [style]="{'display': 'inline-block', 'width':'80px'}"
                [ngModel]="pageSize"
                [outline]="true"
                (ngModelChange)="onTableSizeChange($event)">
                <mdb-select-option *ngFor="let size of tableSizes" [value]="size.value">{{ size.label }}
                </mdb-select-option>
              </mdb-select-2>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</main>

<app-saved-search-edit [isNewRecord]="true"></app-saved-search-edit>
