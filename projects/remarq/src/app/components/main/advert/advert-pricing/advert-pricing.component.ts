import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {DatePipe} from '@angular/common';
import {
  FormBuilder, FormControl, FormGroup,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import {
  AddressService,
  AdvertService, LocaleService,
  EventService,
  SaleService,
  TermsTemplateService
} from '../../../../services';
import {FormsService} from '../../../../services';
import {IMyOptions, ModalDirective, ToastService} from 'ng-uikit-pro-standard';
import {compare} from 'fast-json-patch';
import {AdvertDTO, LocationDTO, SaleDTO, TermsTemplateDTO, User, VehicleDTO} from "../../../../global/interfaces";
import {HelpersService, LoggerService, UserService} from "../../../../global/services";
import {AdvertEventEnum, AdvertStatusEnum, SaleTypeEnum, StatusEnum} from "../../../../global/enums";

@Component({
  selector: 'app-advert-pricing',
  templateUrl: './advert-pricing.component.html',
  styleUrls: ['./advert-pricing.component.scss'],
  providers: [DatePipe]
})
export class AdvertPricingComponent implements OnInit {

  logger = this.logService.taggedLogger(this.constructor?.name);
  public AdvertStatus = AdvertStatusEnum;
  public SaleType = SaleTypeEnum;
  user: User;
  @ViewChild("termsModal") termsModal: ModalDirective;
  @Output() advertChange: EventEmitter<AdvertDTO> = new EventEmitter<AdvertDTO>();
  @Input() vehicle: VehicleDTO;
  form: FormGroup;
  sales: SaleDTO[];
  saleId: string;
  locations: LocationDTO[] = [];
  termsTemplates: TermsTemplateDTO[] = [];
  salesOptions: any;
  managedSales: any[]; // list of managed sales for associating with advert
  locationsOptions: any;
  formState: any;
  saleTypeId: number;
  editingTemplate: TermsTemplateDTO = null;
  public myDatePickerOptions: IMyOptions = {
    dateFormat: 'dd/mm/yyyy'
  };
  availableDate: string;
  availableTime: string;
  endDate: string;
  endTime: string;
  templateName = "Default Template";

  private _formDisabled: boolean;

  constructor(private formBuilder: FormBuilder,
              private salesService: SaleService,
              private addressService: AddressService,
              private datePipe: DatePipe,
              private formService: FormsService,
              private eventService: EventService,
              private logService: LoggerService,
              private helpersService: HelpersService,
              private userService: UserService,
              public localeService: LocaleService,
              private termsService: TermsTemplateService,
              private advertService: AdvertService
  ) {
  }

  private _advert: AdvertDTO;
  xyz: any;

  get advert(): AdvertDTO {
    return this._advert;
  }

  @Input() set advert(advert: AdvertDTO) {
    this._advert = advert;
    this.advertChange.emit(advert);
  }

  @Input('disabledForm') set disabledForm(value: boolean) {
    if (value) {
      console.log("DISABLE FORM");
      if (this.form) {
        this.form.disable();
      }
      this._formDisabled = true;
    } else {
      if (this.form) {
        this.form.enable();
      }
      this._formDisabled = false;
    }
  }

  get f() {
    if (this.form) {
      return this.form?.controls;
    }
    return null;
  }

  async ngOnInit() {

    this.availableDate = this.datePipe.transform(this.advert.availableDate, 'yyyy-MM-dd') || new Date().toString();
    this.availableTime = this.datePipe.transform(this.advert.availableDate, 'HH:mm') || new Date().toString();

    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    this.endDate = this.datePipe.transform(this.advert.endDateTime, 'yyyy-MM-dd' || nextWeek.toString());
    this.endTime = this.datePipe.transform(this.advert.endDateTime, 'HH:mm' || new Date().toString());

    this.logger.info("Advert Listing - Advert:", this.advert);
    this.form = this.formBuilder.group({
      saleId: new UntypedFormControl(this.advert.saleId),
      saleTypeId: new UntypedFormControl(this.advert.sale?.saleTypeId ?? SaleTypeEnum.ManagedSale),
      availableDate: new UntypedFormControl(this.advert.availableDate),
      endDateTime: new UntypedFormControl(this.advert.endDateTime),

      startPrice: new FormControl(this.advert.startPrice, [Validators.pattern(this.formService.numberPattern)]),
      reservePrice: new FormControl(this.advert.reservePrice, [Validators.pattern(this.formService.numberPattern)]),
      bidIncrement: new FormControl(this.advert.bidIncrement, [Validators.pattern(this.formService.numberPattern)]),
      standInValue: new FormControl(this.vehicle.standInValue, [Validators.pattern(this.formService.numberPattern)]),
      buyItNowPrice: new FormControl(this.advert.buyItNowPrice, [Validators.pattern(this.formService.numberPattern)]),
      acceptBids: new FormControl(this.advert.acceptBids),
      autoAcceptBid: new FormControl(this.advert.autoAcceptBid, [Validators.pattern(this.formService.numberPattern)]),
      autoRejectBid: new FormControl(this.advert.autoRejectBid, [Validators.pattern(this.formService.numberPattern)]),
      headline: new FormControl(this.advert.headline),
      termsText: new FormControl(this.helpersService.decodeHtmlEntities(this.advert.termsText)),
      termsTemplateId: new FormControl()
    }, {validator: this.formValidator()});

    if (this._formDisabled == true) {
      this.form.disable();
    }

    this.formState = this.form.value;
    this.saleId = this.advert.saleId;

    // no sale in the advert means it's a managed sale with no sale yet selected
    this.saleTypeId = this.advert.sale?.saleTypeId ?? SaleTypeEnum.ManagedSale;

    this.form.valueChanges.subscribe(x => {

      this.logger.log("Form state: ", this.formState, " -  Form: ", this.form.value);
      let adv = compare(this.formState, this.form.value);
      adv = adv.filter(x => x.path.indexOf('standInValue') == -1);

      if (adv.length > 0) {
        console.log("AD CHANGED");
        this.eventService.AdvertActionEvent.emit({eventType: AdvertEventEnum.DetailsChangedEvent, object: adv});
      }

      let veh = compare(this.formState, this.form.value);

      veh = veh.filter(x => x.path.indexOf('standInValue') > -1);

      if (veh.length > 0) {
        this.eventService.AdvertActionEvent.emit({eventType: AdvertEventEnum.VehicleDetailsChangedEvent, object: veh});
      }

      this.formState = this.form.value;
    });

    await this.userService.loadCurrentUser().then(() => {
      this.user = this.userService.CurrentUser;

      this.termsService.search({
        component: 'advert-pricing',
        filters: {
          customerId: this.advert.customerId,
          hideDeleted: true
        }
      }).then(res => {
        this.termsTemplates = res.results;
      });
    });

    // filters: {includeSaleTypeIds: [SaleTypeEnum.BuyItNow, SaleTypeEnum.TimedSale, SaleTypeEnum.Underwrite]}
    this.salesService.getSales({
      filters: {includeSaleTypeIds: [SaleTypeEnum.BuyItNow, SaleTypeEnum.TimedAuction]}
    })
      .then(result => {
        this.sales = result.sales;
        this.salesOptions = this.sales.map(s => {
          return {label: s.saleName, value: s.saleTypeId};
        });

        // if we're an admin, add the Managed Sale option with a null value
        if (this.user.isAdmin) {
//          this.salesOptions.push({label: 'Managed Sale', value: SaleTypeEnum.ManagedSale});
        }

      });

    this.salesService.getSales({filters: {includeSaleTypeIds: [SaleTypeEnum.ManagedSale]}}).then(result => {
      this.managedSales = result.sales.map(s => {
        return {label: s.saleName, value: s.id};
      });

      this.managedSales.unshift({label: 'None', value: null});
    });

    this.locations = await this.addressService.getCustomerAddresses(this.user.customerId);
    this.locationsOptions = this.locations.map(l => {
      return {label: l.locationName, value: l.id};
    });
  }

  formValidator(): ValidatorFn {

    return (control: UntypedFormGroup): ValidationErrors | null => {

      if (!this.form) {
        return null;
      }

      //this.logger.log("Running form validator: ", control);

      let errors = [];
      let colName = 'buyItNowPrice';
      let buyItNow = control.get(colName).value;

      let colName2 = 'reservePrice';
      let reserve = control.get(colName2).value;

      // only applicable for buy-it-now sales
      if (this.saleTypeId == SaleTypeEnum.BuyItNow) {
        if (!buyItNow || buyItNow <= 0) {
          errors.push("Buy It Now price must be greater than zero");
          this.form.controls[colName].setErrors({buyItNowZero: true});
        } else {
          if (!this.form.controls[colName].errors?.pattern)
            this.form.controls[colName].setErrors(null);
        }
      } else if (this.saleTypeId == SaleTypeEnum.ManagedSale) {
        if ((!buyItNow || buyItNow <= 0) && (!reserve || reserve == 0)) {
          errors.push("Buy It Now or Reserve price must be greater than zero");
          this.form.controls[colName].setErrors({buyItNowZero: true});
        } else {
          if (!this.form.controls[colName].errors?.pattern)
            this.form.controls[colName].setErrors(null);
        }
      }

      colName = 'saleId';
      if (!this.saleId && this.saleTypeId != SaleTypeEnum.ManagedSale) {
        errors.push("Specify a Sale Type for the Advert");
        this.form.controls[colName].setErrors({noSaleId: true});
      } else {
        this.form.controls[colName].setErrors(null);
      }

      colName = 'startPrice';

      const startPrice = control.get(colName).value;

      if (this.saleTypeId != SaleTypeEnum.BuyItNow && (!startPrice || startPrice < 100)) {
        errors.push("Start price must be a minimum of £100");
        this.form.controls[colName].setErrors({startPriceZero: true});
      }

      const obj = {componentName: "listing", errors};
      this.eventService.AdvertActionEvent.emit({eventType: AdvertEventEnum.PublishErrorsSet, object: obj});

      return null;
    };
  }

  getAdvertPatchValues() {
    // use modified form values to populate patch DTO
    const patch = compare(this.advert, this.form.value);
    return patch;
  }

  saleTypeSelected(saleTypeId: any) {

    // Cannot change sale type if there are bids
    if (this.advert.bidCount > 0) {
      return;
    }

    // if saleType has changed we can set the saleId if it is not set to Managed
    if (this.saleTypeId != saleTypeId.value) {

      console.log("THIS ", this.form.value);

      if (this.form.value.startPrice && this.form.value.startPrice < 100) {
        this.form.patchValue({startPrice: 100});
      }

      const sale = this.sales.find(x => x.saleTypeId == saleTypeId.value);
      this.saleTypeId = saleTypeId.value;
      this.saleId = sale?.id;

      this.form.patchValue({saleTypeId: this.saleTypeId});
      this.form.patchValue({saleId: this.saleId});

      // raise event so summary can show appropriate info
      this.eventService.AdvertActionEvent.emit({eventType: AdvertEventEnum.SaleTypeChanged, object: this.saleTypeId});
    }

  }

  availableDateChange(event: any) {
    this.logger.info("Available Date Set", event);
    this.availableDate = event;

    const dt = this.getDateTime(this.availableDate, this.availableTime);
    this.form.patchValue({availableDate: dt});
  }

  availableTimeChange(event: any) {
    this.logger.info("Available Time Set", event);
    this.availableTime = event;

    const dt = this.getDateTime(this.availableDate, this.availableTime);
    this.form.patchValue({availableDate: dt});
  }

  endDateChange(event: any) {
    this.endDate = event;
    const dt = this.getDateTime(this.endDate, this.endTime);
    this.form.patchValue({endDateTime: dt});
    // this.f["endDateTime"].patchValue({ dt } );
  }

  endTimeChange(event: any) {
    this.endTime = event;
    const dt = this.getDateTime(this.endDate, this.endTime);
    this.form.patchValue({endDateTime: dt});
    // this.f["endDateTime"].setValue(dt);
  }

  getDateTime(date, time) {

    if (date && time) {
      return `${date} ${time}`;
    }

    return null;
  }

  managedSaleSelected($event) {
    this.saleId = $event.value;
  }

  saveTemplate() {
    // popup dialog to allow user to enter template name
    this.termsModal.show();
  }

  confirmSaveTemplate() {

    this.termsModal.hide();

    if (this.editingTemplate) {
      this.termsService.patch(this.editingTemplate.id,
        {name: this.templateName}).then(() => {

        this.editingTemplate = null;
        this.termsModal.hide();
      }).catch(() => {
        this.editingTemplate = null;
        this.termsModal.hide();
      });
    } else {
      this.termsService.create({
        customerId: this.advert.customerId,
        name: this.templateName,
        description: this.f.termsText.value
      }).then(res => {
        this.termsTemplates.push(res);
        this.form.patchValue({termsTemplateId: res.id});
      });
    }
  }

  cancelSaveTemplate() {
    this.termsModal.hide();
  }

  applyTemplate() {

    console.log("APPLYING TEMPLATE", this.f.termsTemplateId.value);

    const terms = this.termsTemplates.find(x => x.id === this.f.termsTemplateId.value);

    // add the template to the current description (this will be saved when advert is updated)
    this.form.patchValue({termsText: terms?.description});
  }

  editTemplate() {
    this.editingTemplate = this.termsTemplates.find(x => x.id === this.f.termsTemplateId.value);
    if (this.editingTemplate) {
      this.templateName = this.editingTemplate.name;
      this.termsModal.show();
    }
  }

  deleteTerms(value: any) {

    this.termsService.patch(value, {statusId: StatusEnum.Deleted}).then(() => {
      this.termsTemplates = this.termsTemplates.filter(x => x.id !== value);
    });
  }

  saleTypeExplanation() {
    const response =
      "Buy It Now: The first person to offer the Buy It Now price will win the vehicle. " +
      "Timed Sale: The vehicle will be sold to the highest bidder when the sale ends. " +
      "Managed Sale: Vehicles are lotted and offered for sale sequentially. The sale is managed by an auctioneer. Each lot ends when bidding stops." +
      "Underwrite: Achieve the best price from dealers for a part exchange you have been offered";

    return response;
  }
}
