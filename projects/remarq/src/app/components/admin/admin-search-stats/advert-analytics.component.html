<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">Advert Analytics</h2>
        <div class="d-flex gap-2">
          <button
            class="btn btn-success btn-sm"
            (click)="exportToCSV()"
            [disabled]="exporting || loading">
            <i class="fas fa-file-csv me-2" [class.fa-spin]="exporting"></i>
            Export CSV
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Advert Details Modal -->
<div class="modal fade" [class.show]="showModal" [style.display]="showModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" (click)="hideModal()">
  <div class="modal-dialog modal-lg" role="document" (click)="$event.stopPropagation()">
    <div class="modal-content" *ngIf="selectedAdvert">
      <div class="modal-header">
        <h5 class="modal-title">
          Advert Details: {{ selectedAdvert.vrm }}
        </h5>
        <button type="button" class="close" (click)="hideModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- Advert Summary Cards -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-primary">{{ selectedAdvert.matchedInSavedSearchCount }}</h4>
                <p class="card-text">Saved Search Matches</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-info">{{ selectedAdvert.matchedInUnsavedSearchCount }}</h4>
                <p class="card-text">Unsaved Matches</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-success">{{ selectedAdvert.contactsNotifiedAbout }}</h4>
                <p class="card-text">Contacts Notified</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 [ngClass]="getEffectivenessBadgeClass(selectedAdvert.notificationEffectivenessRate)">
                  {{ selectedAdvert.notificationEffectivenessRate | number:'1.1-1' }}%
                </h4>
                <p class="card-text">Effectiveness</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Advert Details -->
        <div class="row mb-4">
          <div class="col-md-6">
            <h6 class="font-weight-bold">Advert Information</h6>
            <table class="table table-sm">
              <tr>
                <td><strong>VRM:</strong></td>
                <td>{{ selectedAdvert.vrm }}</td>
              </tr>
              <tr>
                <td><strong>Added:</strong></td>
                <td>{{ selectedAdvert.added | date:'medium' }}</td>
              </tr>
              <tr>
                <td><strong>Last Updated:</strong></td>
                <td>{{ selectedAdvert.updated | date:'medium' }}</td>
              </tr>
              <tr>
                <td><strong>Advert Status:</strong></td>
                <td>
                  <span class="badge" [ngClass]="getAdvertStatusBadgeClass(selectedAdvert.advertStatus)">
                    {{ selectedAdvert.advertStatus }}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>Sold Status:</strong></td>
                <td>
                  <span class="badge" [ngClass]="getSoldStatusBadgeClass(selectedAdvert.soldStatus)">
                    {{ selectedAdvert.soldStatus }}
                  </span>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <h6 class="font-weight-bold">Notification Performance</h6>
            <table class="table table-sm">
              <tr>
                <td><strong>Effectiveness Rating:</strong></td>
                <td>
                  <span class="badge" [ngClass]="getEffectivenessBadgeClass(selectedAdvert.notificationEffectivenessRate)">
                    {{ selectedAdvert.notificationEffectiveness }}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>Notification Rate:</strong></td>
                <td>{{ selectedAdvert.notificationEffectivenessRate | number:'1.1-1' }}%</td>
              </tr>
              <tr>
                <td><strong>Total Matches:</strong></td>
                <td>{{ selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount }}</td>
              </tr>
              <tr>
                <td><strong>Match Ratio:</strong></td>
                <td>
                  <div class="progress" style="height: 20px;" *ngIf="(selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount) > 0">
                    <div class="progress-bar bg-primary" role="progressbar"
                         [style.width.%]="(selectedAdvert.matchedInSavedSearchCount / (selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount)) * 100">
                      Saved: {{ selectedAdvert.matchedInSavedSearchCount }}
                    </div>
                    <div class="progress-bar bg-secondary" role="progressbar"
                         [style.width.%]="(selectedAdvert.matchedInUnsavedSearchCount / (selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount)) * 100">
                      Unsaved: {{ selectedAdvert.matchedInUnsavedSearchCount }}
                    </div>
                  </div>
                  <span *ngIf="(selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount) === 0" class="text-muted">
                    No matches found
                  </span>
                </td>
              </tr>
            </table>
          </div>
        </div>

        <!-- Performance Insights -->
        <div class="row">
          <div class="col-12">
            <h6 class="font-weight-bold mb-3">Performance Insights</h6>
            <div class="alert"
                 [class.alert-success]="selectedAdvert.notificationEffectivenessRate >= 70"
                 [class.alert-warning]="selectedAdvert.notificationEffectivenessRate >= 40 && selectedAdvert.notificationEffectivenessRate < 70"
                 [class.alert-danger]="selectedAdvert.notificationEffectivenessRate < 40">
              <div *ngIf="selectedAdvert.notificationEffectivenessRate >= 70">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Excellent Performance:</strong> This advert has a high notification effectiveness rate, indicating good match quality with saved searches.
              </div>
              <div *ngIf="selectedAdvert.notificationEffectivenessRate >= 40 && selectedAdvert.notificationEffectivenessRate < 70">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Moderate Performance:</strong> This advert shows moderate notification effectiveness. Consider reviewing search criteria matching.
              </div>
              <div *ngIf="selectedAdvert.notificationEffectivenessRate < 40">
                <i class="fas fa-times-circle me-2"></i>
                <strong>Poor Performance:</strong> This advert has low notification effectiveness. Review why matches aren't converting to notifications.
              </div>
            </div>

            <!-- Additional Insights based on data -->
            <div class="row mt-3">
              <div class="col-md-6">
                <div class="card border-left-info">
                  <div class="card-body">
                    <h6 class="card-title">
                      <i class="fas fa-info-circle text-info me-2"></i>
                      Match Analysis
                    </h6>
                    <p class="card-text small mb-0">
                      <span *ngIf="selectedAdvert.matchedInSavedSearchCount > selectedAdvert.matchedInUnsavedSearchCount">
                        This advert primarily matches saved searches ({{ selectedAdvert.matchedInSavedSearchCount }} vs {{ selectedAdvert.matchedInUnsavedSearchCount }}),
                        indicating good alignment with customer preferences.
                      </span>
                      <span *ngIf="selectedAdvert.matchedInSavedSearchCount <= selectedAdvert.matchedInUnsavedSearchCount">
                        This advert has more unsaved matches ({{ selectedAdvert.matchedInUnsavedSearchCount }}) than saved searches ({{ selectedAdvert.matchedInSavedSearchCount }}),
                        suggesting potential for new customer interest.
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card border-left-warning">
                  <div class="card-body">
                    <h6 class="card-title">
                      <i class="fas fa-lightbulb text-warning me-2"></i>
                      Recommendations
                    </h6>
                    <p class="card-text small mb-0">
                      <span *ngIf="selectedAdvert.notificationEffectivenessRate < 30">
                        Consider reviewing why notifications aren't being sent despite matches. Check notification settings and customer preferences.
                      </span>
                      <span *ngIf="selectedAdvert.notificationEffectivenessRate >= 30 && selectedAdvert.notificationEffectivenessRate < 70">
                        Good notification rate but room for improvement. Review search criteria precision and notification timing.
                      </span>
                      <span *ngIf="selectedAdvert.notificationEffectivenessRate >= 70">
                        Excellent performance! This advert is well-matched to customer saved searches and notification preferences.
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideModal()">Close</button>
        <button type="button" class="btn btn-primary" onclick="window.print()">
          <i class="fas fa-print me-2"></i>
          Print Details
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Backdrop -->
<div class="modal-backdrop fade" [class.show]="showModal" *ngIf="showModal"></div>

<!-- Filters Card -->
<div class="card shadow mb-4">
  <div class="card-header" (click)="toggleFilters()" style="cursor: pointer;">
    <h6 class="m-0 font-weight-bold text-primary">
      <i class="fas fa-filter me-2"></i>
      Advert Analytics Filters
      <i class="fas" [ngClass]="showFilters ? 'fa-chevron-up' : 'fa-chevron-down'" style="float: right;"></i>
    </h6>
  </div>
  <div class="card-body" [ngClass]="{'collapse': !showFilters, 'show': showFilters}">
    <div class="row">
      <!-- Date Range -->
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">From Date</label>
        <input
          type="date"
          class="form-control form-control-sm"
          [(ngModel)]="fromDate"
          (ngModelChange)="onFilterChanged()">
      </div>
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">To Date</label>
        <input
          type="date"
          class="form-control form-control-sm"
          [(ngModel)]="toDate"
          (ngModelChange)="onFilterChanged()">
      </div>

      <!-- VRM Search -->
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">VRM</label>
        <input
          type="text"
          class="form-control form-control-sm"
          placeholder="Search by VRM..."
          [(ngModel)]="vrmFilter"
          (ngModelChange)="onFilterChanged()">
      </div>

      <!-- Status Filters -->
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Advert Status</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="advertStatusFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All Statuses</option>
          <option *ngFor="let status of advertStatusOptions" [value]="status.value">
            {{ status.text }}
          </option>
        </select>
      </div>

      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Sold Status</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="soldStatusFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All</option>
          <option *ngFor="let status of soldStatusOptions" [value]="status.value">
            {{ status.text }}
          </option>
        </select>
      </div>

      <!-- Notification Rate Range -->
<!--      <div class="col-lg-3 col-md-6 mb-3">-->
<!--        <label class="form-label">Min Notification Rate %</label>-->
<!--        <input-->
<!--          type="number"-->
<!--          class="form-control form-control-sm"-->
<!--          placeholder="0"-->
<!--          min="0"-->
<!--          max="100"-->
<!--          [(ngModel)]="minNotificationRate"-->
<!--          (ngModelChange)="onFilterChanged()">-->
<!--      </div>-->
<!--      <div class="col-lg-3 col-md-6 mb-3">-->
<!--        <label class="form-label">Max Notification Rate %</label>-->
<!--        <input-->
<!--          type="number"-->
<!--          class="form-control form-control-sm"-->
<!--          placeholder="100"-->
<!--          min="0"-->
<!--          max="100"-->
<!--          [(ngModel)]="maxNotificationRate"-->
<!--          (ngModelChange)="onFilterChanged()">-->
<!--      </div>-->

      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Vendor</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="vendorFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All</option>
          <option *ngFor="let vendor of vendors" [value]="vendor.id">
            {{ vendor.vendorName }}
          </option>
        </select>
      </div>


      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Email Issues</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="emailIssuesFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All Adverts</option>
          <option value="high_bounce">High Bounce Rate (>5%)</option>
          <option value="no_emails">No Email Activity</option>
          <option value="low_engagement">Low Engagement (<10%)</option>
          <option value="blocked_emails">Blocked Emails</option>
        </select>
      </div>

      <!-- Clear Filters Button -->
      <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-end">
        <button
          class="btn btn-outline-secondary btn-sm w-100"
          (click)="clearFilters()">
          <i class="fas fa-times me-2"></i>
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Error State -->
<div *ngIf="error" class="alert alert-danger" role="alert">
  <i class="fas fa-exclamation-triangle me-2"></i>
  {{ error }}
</div>

<!-- Data Table -->
<div class="card shadow">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h6 class="m-0 font-weight-bold text-primary">
        Advert Analytics
        <span *ngIf="totalCount > 0" class="badge badge-secondary ms-2">{{ totalCount }}</span>
      </h6>
      <div *ngIf="loading" class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>

  <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;">
    <div class="table-responsive">
      <table class="table table-striped table-hover mb-0">
        <thead class="thead-light">
        <tr>
          <th scope="col" class="cursor-pointer" (click)="onSort('vrm')">
            VRM
            <i class="ms-1" [ngClass]="getSortIcon('vrm')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('customerName')">
            Vendor
            <i class="ms-1" [ngClass]="getSortIcon('customerName')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('advertStatus')">
            Advert Status
            <i class="ms-1" [ngClass]="getSortIcon('advertStatus')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('soldStatus')">
            Sold Status
            <i class="ms-1" [ngClass]="getSortIcon('soldStatus')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('added')">
            Added
            <i class="ms-1" [ngClass]="getSortIcon('added')"></i>
          </th>
          <th>
            Search Matches
          </th>
          <th>
            Contacts Notified
          </th>
          <th>
            Effectiveness
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('emailsSent')">
            Email Sends
            <i class="ms-1" [ngClass]="getSortIcon('emailsSent')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('emailOpenRate')">
            Email Open Rate
            <i class="ms-1" [ngClass]="getSortIcon('emailOpenRate')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('emailEngagementScore')">
            Email Engagement
            <i class="ms-1" [ngClass]="getSortIcon('emailEngagementScore')"></i>
          </th>
          <th scope="col">Details</th>
        </tr>
        </thead>
        <tbody>
        <ng-container *ngFor="let advert of adverts | paginate: { itemsPerPage: pageSize, currentPage: page, totalItems: count }; let i = index">
        <tr [class.table-info]="advert.isOpen">
          <td>
            <span class="font-weight-bold">{{ advert.vrm }}</span>
            <div>
              <small>{{ advert.description }}</small>
            </div>
            <div class="d-flex flex-row" style="justify-content: space-between;">
              <div class="btn btn-primary btn-xs">
                <a [routerLink]="['/admin/manage-negotiations', advert.advertId]" target="_blank">
                  Brokerage
                </a>
              </div>
              <div class="btn btn-secondary btn-xs">
                <a [routerLink]="['/main/listing', advert.advertId]" target="_blank">
                  View Listing
                </a>
              </div>
            </div>
          </td>
          <td>
            <a [routerLink]="['/admin/customer', advert.customerId]" target="_blank">{{ advert.customerName }}</a>
          </td>
          <td>
                <span class="badge" [ngClass]="getAdvertStatusBadgeClass(advert.advertStatus)">
                  {{ getAdvertStatusText(advert.advertStatus) }}
                </span>
          </td>
          <td>
                <span class="badge" [ngClass]="getSoldStatusBadgeClass(advert.soldStatus)">
                  {{ getSoldStatusText(advert.soldStatus) }}
                </span>
          </td>
          <td>
            <small>{{ advert.added | date:'short' }}</small>
          </td>
          <td>
            <div class="d-flex flex-column">
              <span class="badge badge-primary mb-1">{{ advert.matchedInSavedSearchCount }} Saved</span>
              <span class="badge badge-secondary">{{ advert.matchedInUnsavedSearchCount }} Unsaved</span>
            </div>
          </td>
          <td>
            <span class="badge badge-success">{{ advert.contactsNotifiedAbout }}</span>
          </td>
          <td>
            <div class="d-flex flex-column">
                  <span
                    class="badge mb-1"
                    [ngClass]="getEffectivenessBadgeClass(advert.notificationEffectivenessRate)">
                    {{ advert.notificationEffectivenessRate | number:'1.1-1' }}%
                  </span>
              <small class="text-muted">{{ advert.notificationEffectiveness }}</small>
            </div>
          </td>
          <td>
            <div class="d-flex flex-column">
              <span class="badge bg-info">{{ formatNumber(advert.emailsSent || 0) }}</span>
              <small class="text-muted" *ngIf="advert.emailsDelivered > 0">
                <span
                  class="badge bg-success cursor-pointer"
                  (click)="showCustomerBreakdown(advert.advertId, 'delivered')"
                  title="Click to see customers who received emails">
                  {{ formatNumber(advert.emailsDelivered) }} delivered
                </span>
              </small>
            </div>
          </td>
          <td>
            <div class="d-flex flex-column" *ngIf="advert.emailsSent > 0; else noEmails">
              <span
                class="badge mb-1 cursor-pointer"
                [ngClass]="getEmailEngagementBadgeClass(advert.emailOpenRate / 100)"
                (click)="showCustomerBreakdown(advert.advertId, 'opened')"
                title="{{ formatPercentage(advert.emailOpenRate / 100) }} open rate - click to see customers who opened">
                {{ formatPercentage(advert.emailOpenRate / 100) }}
              </span>
              <small class="text-muted">
                <span
                  class="cursor-pointer text-decoration-underline"
                  (click)="showCustomerBreakdown(advert.advertId, 'opened')"
                  title="Click to see customers who opened emails">
                  {{ formatNumber(advert.emailsOpened || 0) }} opens
                </span>
              </small>
            </div>
            <ng-template #noEmails>
              <span class="text-muted">No emails</span>
            </ng-template>
          </td>
          <td>
            <div class="d-flex flex-column" *ngIf="advert.emailsSent > 0; else noEmailEngagement">
              <span
                class="badge mb-1 cursor-pointer"
                [ngClass]="getEmailEngagementBadgeClass(advert.emailEngagementScore / 100)"
                (click)="showCustomerBreakdown(advert.advertId, 'clicked')"
                title="{{ advert.emailEngagementScore | number:'1.1-1' }}% engagement score - click to see customers who clicked">
                {{ advert.emailEngagementScore | number:'1.1-1' }}
              </span>
              <small class="text-muted cursor-pointer"
                     [ngClass]="getEmailEngagementRating(advert.emailEngagementScore / 100) === 'Excellent' ? 'text-success' :
                                getEmailEngagementRating(advert.emailEngagementScore / 100) === 'Good' ? 'text-warning' : 'text-danger'"
                     (click)="showCustomerBreakdown(advert.advertId, 'clicked')"
                     title="Click to see customers who clicked on emails">
                {{ getEmailEngagementRating(advert.emailEngagementScore / 100) }}
              </small>
            </div>
            <ng-template #noEmailEngagement>
              <span class="text-muted">No data</span>
            </ng-template>
          </td>
          <td class="text-center" (click)="$event.stopPropagation()">
            <button class="btn btn-outline-primary btn-xs me-2" (click)="showAdvertDetails(advert)" title="View advert details">
              <i class="fas fa-info-circle"></i>
            </button>
            <i class="fas ms-2 fa-lg"
               [class.fa-chevron-circle-down]="!isAdvertOpen(advert)"
               [class.fa-chevron-circle-up]="isAdvertOpen(advert)"
               [title]="isAdvertOpen(advert) ? 'Click to hide email metrics' : 'Click to show email metrics'"
               (click)="toggleAdvertAccordion(advert); $event.stopPropagation()"
               style="cursor: pointer; color: #007bff;"></i>
          </td>
        </tr>

        <!-- Email Metrics Accordion Row -->
        <tr *ngIf="isAdvertOpen(advert)" class="email-metrics-row">
          <td colspan="11" class="p-0">
            <div class="bg-light border-top">
              <div class="p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <h6 class="mb-0 text-info">
                    <i class="fas fa-envelope-open me-2"></i>
                    Email Analytics for {{ advert.vrm }}
                  </h6>
                </div>

                <div *ngIf="advert.emailsSent; else noEmailData" class="row">
                  <div class="col-md-3">
                    <div class="card border-0 bg-white">
                      <div class="card-body text-center p-2">
                        <div class="text-success h4 mb-1">{{ formatNumber(advert.emailsDelivered || 0) }}</div>
                        <div class="small text-muted">
                          <span class="cursor-pointer badge bg-success"
                                (click)="showCustomerBreakdown(advert.advertId, 'delivered')"
                                title="Click to see customers who received emails">
                            Delivered
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-0 bg-white">
                      <div class="card-body text-center p-2">
                        <div class="text-primary h4 mb-1">{{ formatNumber(advert.emailsOpened || 0) }}</div>
                        <div class="small text-muted">
                          <span class="cursor-pointer badge bg-primary"
                                (click)="showCustomerBreakdown(advert.advertId, 'opened')"
                                title="{{ formatPercentage((advert.emailsOpened || 0) / (advert.emailsDelivered || 1)) }} open rate - click for details">
                            Opened
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-0 bg-white">
                      <div class="card-body text-center p-2">
                        <div class="text-warning h4 mb-1">{{ formatNumber(Math.round((advert.emailsOpened || 0) * 0.15)) }}</div>
                        <div class="small text-muted">
                          <span class="cursor-pointer badge bg-warning"
                                (click)="showCustomerBreakdown(advert.advertId, 'clicked')"
                                title="Estimated click rate - click for details">
                            Clicked
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-0 bg-white">
                      <div class="card-body text-center p-2">
                        <div class="text-info h4 mb-1">{{ formatPercentage((advert.emailsOpened || 0) / (advert.emailsDelivered || 1)) }}</div>
                        <div class="small text-muted">Open Rate</div>
                      </div>
                    </div>
                  </div>
                </div>

                <ng-template #noEmailData>
                  <div class="text-center py-4">
                    <i class="fas fa-envelope-open-text fa-2x text-muted mb-2"></i>
                    <p class="text-muted">No email analytics available for this advert</p>
                  </div>
                </ng-template>
              </div>
            </div>
          </td>
        </tr>
        </ng-container>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && adverts.length === 0" class="text-center py-5">
      <i class="fas fa-car fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">No adverts found</h5>
      <p class="text-muted">Try adjusting your filters or search criteria.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div class="card-footer" *ngIf="totalCount > 0">
    <div class="d-flex justify-content-between align-items-center">
      <div class="text-muted">
        Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to
        {{ Math.min(currentPage * itemsPerPage, totalCount) }} of
        {{ totalCount }} adverts
      </div>
      <div>
        Items per Page:
        <select (change)="handlePageSizeChange($event)">
          <option *ngFor="let size of pageSizes" [ngValue]="size">
            {{ size }}
          </option>
        </select>
      </div>
      <pagination-controls
        class="paginator"
        previousLabel="Previous"
        nextLabel="Next"
        (pageChange)="onTableDataChange($event)">
      </pagination-controls>
    </div>
  </div>


<!-- Email Analytics Section -->
<div *ngIf="showEmailMetrics" class="mt-5">
  <div class="card shadow">
    <div class="card-header bg-info text-white">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="fas fa-envelope-open me-2"></i>
          Email Analytics for Adverts
        </h5>
        <div class="d-flex align-items-center gap-3">
          <button
            class="btn btn-outline-light btn-sm"
            (click)="clearEmailFilters()"
            *ngIf="minOpenRate || maxOpenRate || minClickRate || maxClickRate">
            <i class="fas fa-times me-1"></i>
            Clear Email Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Email Filters -->
    <div class="card-body border-bottom">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label">Min Open Rate (%)</label>
          <input
            type="number"
            class="form-control form-control-sm"
            [(ngModel)]="minOpenRate"
            (ngModelChange)="onEmailMetricsFilterChanged()"
            placeholder="e.g. 10"
            min="0"
            max="100">
        </div>
        <div class="col-md-3">
          <label class="form-label">Max Open Rate (%)</label>
          <input
            type="number"
            class="form-control form-control-sm"
            [(ngModel)]="maxOpenRate"
            (ngModelChange)="onEmailMetricsFilterChanged()"
            placeholder="e.g. 50"
            min="0"
            max="100">
        </div>
        <div class="col-md-3">
          <label class="form-label">Min Click Rate (%)</label>
          <input
            type="number"
            class="form-control form-control-sm"
            [(ngModel)]="minClickRate"
            (ngModelChange)="onEmailMetricsFilterChanged()"
            placeholder="e.g. 1"
            min="0"
            max="100"
            step="0.1">
        </div>
        <div class="col-md-3">
          <label class="form-label">Max Click Rate (%)</label>
          <input
            type="number"
            class="form-control form-control-sm"
            [(ngModel)]="maxClickRate"
            (ngModelChange)="onEmailMetricsFilterChanged()"
            placeholder="e.g. 10"
            min="0"
            max="100"
            step="0.1">
        </div>
      </div>
    </div>

    <!-- Email Metrics Loading State -->
    <div *ngIf="emailMetricsLoading" class="card-body text-center py-5">
      <div class="spinner-border text-info" role="status">
        <span class="visually-hidden">Loading email metrics...</span>
      </div>
      <p class="mt-2 text-muted">Loading email analytics data...</p>
    </div>

    <!-- Email Metrics Table -->
    <div class="card-body" *ngIf="!emailMetricsLoading">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th scope="col">Vehicle</th>
              <th scope="col">
                <button class="btn btn-link p-0 text-decoration-none" (click)="onSort('emailsDelivered')">
                  Email Volume
                  <i [class]="getSortIcon('emailsDelivered')"></i>
                </button>
              </th>
              <th scope="col">
                <button class="btn btn-link p-0 text-decoration-none" (click)="onSort('openRate')">
                  Open Rate
                  <i [class]="getSortIcon('openRate')"></i>
                </button>
              </th>
              <th scope="col">
                <button class="btn btn-link p-0 text-decoration-none" (click)="onSort('clickThroughRate')">
                  Click Rate
                  <i [class]="getSortIcon('clickThroughRate')"></i>
                </button>
              </th>
              <th scope="col">
                <button class="btn btn-link p-0 text-decoration-none" (click)="onSort('engagementScore')">
                  Engagement Score
                  <i [class]="getSortIcon('engagementScore')"></i>
                </button>
              </th>
              <th scope="col">Performance</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let emailAdvert of emailAdverts">
              <td>
                <div>
                  <span class="fw-bold">{{ emailAdvert.vrm }}</span>
                  <div class="text-muted small">{{ emailAdvert.make }} {{ emailAdvert.model }}</div>
                  <div class="text-muted small">{{ emailAdvert.year }} • £{{ formatNumber(emailAdvert.price) }}</div>
                </div>
              </td>
              <td>
                <div class="d-flex flex-column">
                  <span class="badge bg-success mb-1">{{ formatNumber(emailAdvert.emailsDelivered) }} delivered</span>
                  <span class="badge bg-info mb-1">{{ formatNumber(emailAdvert.emailsOpened) }} opened</span>
                  <span class="badge bg-primary">{{ formatNumber(emailAdvert.emailsClicked) }} clicked</span>
                </div>
              </td>
              <td>
                <span [class]="getEmailEngagementBadgeClass(emailAdvert.openRate)">
                  {{ formatPercentage(emailAdvert.openRate, 1) }}
                </span>
                <div class="text-muted small mt-1">
                  {{ getEmailEngagementRating(emailAdvert.openRate) }}
                </div>
              </td>
              <td>
                <span [class]="getEmailEngagementBadgeClass(emailAdvert.clickThroughRate)">
                  {{ formatPercentage(emailAdvert.clickThroughRate, 2) }}
                </span>
                <div class="text-muted small mt-1">
                  {{ getEmailEngagementRating(emailAdvert.clickThroughRate) }}
                </div>
              </td>
              <td>
                <div class="d-flex align-items-center">
                  <div class="progress me-2" style="width: 80px; height: 20px;">
                    <div
                      class="progress-bar"
                      [class.bg-success]="emailAdvert.engagementScore >= 0.15"
                      [class.bg-warning]="emailAdvert.engagementScore >= 0.05 && emailAdvert.engagementScore < 0.15"
                      [class.bg-danger]="emailAdvert.engagementScore < 0.05"
                      role="progressbar"
                      [style.width.%]="Math.min(emailAdvert.engagementScore * 500, 100)">
                    </div>
                  </div>
                  <span class="small fw-bold">{{ formatPercentage(emailAdvert.engagementScore, 1) }}</span>
                </div>
              </td>
              <td>
                <div class="d-flex flex-column">
                  <div class="small mb-1">
                    <span class="text-muted">Bounce:</span>
                    <span class="badge bg-warning text-dark ms-1">
                      {{ formatPercentage(emailAdvert.bounceRate, 1) }}
                    </span>
                  </div>
                  <div class="small text-muted">
                    First: {{ emailAdvert.firstEmailSent | date:'short' }}
                  </div>
                  <div class="small text-muted">
                    Last: {{ emailAdvert.lastEmailSent | date:'short' }}
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Email Metrics Empty State -->
      <div *ngIf="!emailMetricsLoading && emailAdverts.length === 0" class="text-center py-5">
        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No email metrics found</h5>
        <p class="text-muted">No email analytics data available for the current filters and date range.</p>
      </div>
    </div>

    <!-- Email Metrics Pagination -->
    <div class="card-footer" *ngIf="totalEmailAdverts > 0 && !emailMetricsLoading">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Showing {{ ((currentEmailPage - 1) * itemsPerPage) + 1 }} to
          {{ Math.min(currentEmailPage * itemsPerPage, totalEmailAdverts) }} of
          {{ totalEmailAdverts }} adverts with email metrics
        </div>
        <pagination-controls
          id="emailPaginator"
          (pageChange)="onEmailMetricsPageChanged($event)"
          [directionLinks]="true"
          previousLabel="Previous"
          nextLabel="Next">
        </pagination-controls>
      </div>
    </div>
  </div>
</div>
