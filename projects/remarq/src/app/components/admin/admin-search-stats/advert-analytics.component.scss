// Advert Analytics Component Styles

.cursor-pointer {
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: #f8f9fc;
  }
}

.table {
  th, td {
    vertical-align: middle;
  }

  th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
  }
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.modal {
  .progress {
    background-color: #e9ecef;

    .progress-bar {
      font-size: 0.75rem;
      text-align: center;
      line-height: 20px;
    }
  }

  .card {
    border: 1px solid #e3e6f0;

    .card-body h4 {
      margin-bottom: 0;
    }
  }
}

.alert {
  border: none;
  border-radius: 0.35rem;

  i {
    margin-right: 0.5rem;
  }
}

// Email metrics accordion
.email-metrics-row {
  background-color: #f8f9fc;
  
  .bg-light {
    background-color: #f8f9fc !important;
  }
  
  .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
    
    &:hover {
      box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }
  }
}

// Collapsible filter section
.collapse:not(.show) {
  display: none;
}

.collapse.show {
  display: block;
}

// Custom scrollbar for results table
.card-body {
  scrollbar-width: thin;
  scrollbar-color: #6c757d #e9ecef;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #e9ecef;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 4px;
    
    &:hover {
      background: #495057;
    }
  }
}

// Extra small button style
.btn-xs {
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  line-height: 1.2;
  border-radius: 0.2rem;
}

// Chevron icons styling
.fa-chevron-circle-down,
.fa-chevron-circle-up {
  color: #6c757d;
  cursor: pointer;
  transition: color 0.15s ease-in-out;
  
  &:hover {
    color: #495057;
  }
}

@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.8rem;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
  
  .email-metrics-row {
    .row {
      margin: 0;
    }
    
    .col-md-3 {
      padding: 0.25rem;
    }
  }
}
