import { Component, OnInit } from '@angular/core';
import {AdminStatVendorDTO, AdvertAnalyticsSearchDTO, AdvertNotificationStatsDTO, AdvertEmailMetricsDTO, AdvertEmailSearchDTO} from '../../../global/interfaces';
import {AdvertStatusEnum, SoldStatusEnum} from '../../../global/enums';
import { AdminSearchStatsService, EmailAnalyticsService } from '../services';

@Component({
  selector: 'app-advert-analytics',
  templateUrl: './advert-analytics.component.html',
  styleUrls: ['./advert-analytics.component.scss']
})
export class AdvertAnalyticsComponent implements OnInit {
  adverts: AdvertNotificationStatsDTO[] = [];
  totalCount = 0;
  currentPage = 1;
  itemsPerPage = 20;
  loading = false;
  exporting = false;
  error: string | null = null;

  pageSizes = [10, 20, 50, 100];


  // Filter properties
  vrmFilter = '';
  advertStatusFilter = '';
  soldStatusFilter = '';
  fromDate: Date;
  toDate: Date;
  minNotificationRate: number | null = null;
  maxNotificationRate: number | null = null;
  vendorFilter: string = '';

  vendors: AdminStatVendorDTO[];

  // Sorting
  sortColumn = 'notificationEffectivenessRate';
  sortDescending = true;

  // Dropdown options
  advertStatusOptions = Object.keys(AdvertStatusEnum)
    .filter(key => isNaN(Number(key)))
    .map(key => ({
      value: AdvertStatusEnum[key as keyof typeof AdvertStatusEnum],
      text: key
    }));

  soldStatusOptions = Object.keys(SoldStatusEnum)
    .filter(key => isNaN(Number(key)))
    .map(key => ({
      value: SoldStatusEnum[key as keyof typeof SoldStatusEnum],
      text: key
    }));

  // Modal state
  selectedAdvert: AdvertNotificationStatsDTO | null = null;
  showModal = false;

  // Email analytics properties
  showEmailMetrics = false;
  emailMetricsLoading = false;
  emailAdverts: AdvertEmailMetricsDTO[] = [];
  totalEmailAdverts = 0;
  currentEmailPage = 1;

  // Email filter properties (for integrated filters in main table)
  minEmailOpenRate: number | null = null;
  maxEmailOpenRate: number | null = null;
  emailIssuesFilter: string = '';

  // Email filter properties (for separate email analytics section)
  minOpenRate: number | null = null;
  maxOpenRate: number | null = null;
  minClickRate: number | null = null;
  maxClickRate: number | null = null;

  // Collapsible filters
  showFilters = true;

  // Properties to match working pagination example
  page = 1;
  pageSize = 10;
  count = 0;


  private syncPaginationProperties() {
    this.pageSize = this.itemsPerPage;
    this.page = this.currentPage;
    this.count = this.totalCount;
  }

  constructor(
    private searchAnalyticsService: AdminSearchStatsService,
    private emailAnalyticsService: EmailAnalyticsService
  ) {
    // Set default date range to last 30 days
    this.toDate = new Date();
    this.fromDate = new Date();
    this.fromDate.setDate(this.fromDate.getDate() - 30);
  }

  async ngOnInit() {
    this.syncPaginationProperties();
    this.loadVendors();
    await this.loadAdverts();
  }

  loadVendors() {
    this.searchAnalyticsService.getVendors().then(res => {
      this.vendors = res;
    })
  }

  async loadAdverts() {
    this.loading = true;
    this.error = null;

    try {
      const searchDTO = this.buildSearchDTO();
      const result = await this.searchAnalyticsService.getAdvertAnalytics(searchDTO);

      this.adverts = (result.results || []).map(advert => {
        advert.isOpen = false; // Add isOpen property for accordion
        return advert;
      });
      this.totalCount = result.totalItems || 0;
      this.syncPaginationProperties(); // This will sync count with totalCount
    } catch (error) {
      this.error = 'Failed to load advert analytics';
      console.error('Advert analytics load error:', error);
    } finally {
      this.loading = false;
    }
  }

  async onPageChanged(page: number) {
    console.log("** onPageChanged: ", page);

    this.currentPage = page;
    await this.loadAdverts();
  }

  async onTableDataChange(pageNum: number) {
    // Update both sets of properties to keep them in sync
    this.currentPage = pageNum;
    this.page = pageNum;

    await this.loadAdverts();
  }

  async manualPageChange(page: number) {
    this.currentPage = page;
    await this.loadAdverts();
  }

  async onFilterChanged() {
    this.currentPage = 1; // Reset to first page
    await this.loadAdverts();
  }

  async onSort(column: string) {
    if (this.sortColumn === column) {
      this.sortDescending = !this.sortDescending;
    } else {
      this.sortColumn = column;
      this.sortDescending = true;
    }

    this.currentPage = 1;
    await this.loadAdverts();
  }

  async exportToCSV() {
    this.exporting = true;

    try {
      const searchDTO = this.buildSearchDTO();
      await this.searchAnalyticsService.exportAdvertAnalytics(searchDTO);
    } catch (error) {
      console.error('Export error:', error);
    } finally {
      this.exporting = false;
    }
  }

  clearFilters() {
    this.vrmFilter = '';
    this.advertStatusFilter = '';
    this.soldStatusFilter = '';
    this.minNotificationRate = null;
    this.maxNotificationRate = null;
    // Note: removed minEmailOpenRate and maxEmailOpenRate from main filters
    this.emailIssuesFilter = '';
    this.fromDate = new Date();
    this.fromDate.setDate(this.fromDate.getDate() - 30);
    this.toDate = null;
    this.onFilterChanged();
  }

  showAdvertDetails(advert: AdvertNotificationStatsDTO) {
    this.selectedAdvert = advert;
    this.showModal = true;
  }

  hideModal() {
    this.showModal = false;
    this.selectedAdvert = null;
  }

  getEffectivenessBadgeClass(rate: number): string {
    return this.searchAnalyticsService.getEffectivenessBadgeClass(rate);
  }

  getEffectivenessRating(rate: number): string {
    return this.searchAnalyticsService.getEffectivenessRating(rate);
  }

  getAdvertStatusBadgeClass(statusId: number): string {
    return this.searchAnalyticsService.getAdvertStatusBadgeClass(statusId);
  }

  getSoldStatusBadgeClass(statusId: number): string {
    return this.searchAnalyticsService.getSoldStatusBadgeClass(statusId);
  }

  getSoldStatusText(statusId: number): string {
    return this.searchAnalyticsService.getSoldStatusText(statusId);
  }

  getAdvertStatusText(statusId: number): string {
    return this.searchAnalyticsService.getAdvertStatusText(statusId);
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) return 'fas fa-sort';
    return this.sortDescending ? 'fas fa-sort-down' : 'fas fa-sort-up';
  }

  private buildSearchDTO(): AdvertAnalyticsSearchDTO {
    const offset = (this.currentPage - 1) * this.itemsPerPage;

    return this.searchAnalyticsService.buildDefaultAdvertSearchDTO({
      offset,
      limit: this.itemsPerPage,
      filters: {
        vrm: this.vrmFilter || undefined,
        advertStatus: this.advertStatusFilter || undefined,
        soldStatus: this.soldStatusFilter || undefined,
        fromDate: this.fromDate,
        toDate: this.toDate,
        vendorId: this.vendorFilter || undefined,
        minNotificationRate: this.minNotificationRate || undefined,
        maxNotificationRate: this.maxNotificationRate || undefined
      },
      order: [{ column: this.sortColumn, descending: this.sortDescending }]
    });
  }

  protected readonly Math = Math;

  handlePageSizeChange(event: any) {

    console.log("** handlePageSizeChange: ", event);

    this.itemsPerPage = event.target.value;
    this.currentPage = 1;

    this.loadAdverts();
  }

  // Email Analytics Methods

  async toggleEmailMetrics() {
    this.showEmailMetrics = !this.showEmailMetrics;
    if (this.showEmailMetrics && this.emailAdverts.length === 0) {
      await this.loadEmailMetrics();
    }
  }

  async loadEmailMetrics() {
    this.emailMetricsLoading = true;

    try {
      const searchDTO = this.buildEmailMetricsSearchDTO();
      const result = await this.emailAnalyticsService.getAdvertEmailMetrics(searchDTO).toPromise();

      this.emailAdverts = result?.results || [];
      this.totalEmailAdverts = result?.totalItems || 0;
    } catch (error) {
      console.error('Email metrics load error:', error);
    } finally {
      this.emailMetricsLoading = false;
    }
  }

  async onEmailMetricsFilterChanged() {
    this.currentEmailPage = 1;
    await this.loadEmailMetrics();
  }

  async onEmailMetricsPageChanged(page: number) {
    this.currentEmailPage = page;
    await this.loadEmailMetrics();
  }

  private buildEmailMetricsSearchDTO(): AdvertEmailSearchDTO {
    const offset = (this.currentEmailPage - 1) * this.itemsPerPage;

    return this.emailAnalyticsService.buildDefaultAdvertEmailSearchDTO({
      offset,
      limit: this.itemsPerPage,
      startDate: this.fromDate,
      endDate: this.toDate,
      vrm: this.vrmFilter || undefined,
      minOpenRate: this.minOpenRate ? this.minOpenRate / 100 : undefined,
      maxOpenRate: this.maxOpenRate ? this.maxOpenRate / 100 : undefined,
      minClickRate: this.minClickRate ? this.minClickRate / 100 : undefined,
      maxClickRate: this.maxClickRate ? this.maxClickRate / 100 : undefined,
      sortBy: this.sortColumn,
      sortDescending: this.sortDescending
    });
  }

  clearEmailFilters() {
    this.minOpenRate = null;
    this.maxOpenRate = null;
    this.minClickRate = null;
    this.maxClickRate = null;
    this.onEmailMetricsFilterChanged();
  }

  // Email Analytics Utility Methods
  getEmailEngagementBadgeClass(rate: number): string {
    return this.emailAnalyticsService.getEngagementBadgeClass(rate);
  }

  getEmailEngagementRating(rate: number): string {
    return this.emailAnalyticsService.getEngagementRating(rate);
  }

  formatPercentage(value: number, decimals: number = 1): string {
    return this.emailAnalyticsService.formatPercentage(value, decimals);
  }

  formatNumber(value: number): string {
    return this.emailAnalyticsService.formatNumber(value);
  }

  // Filter collapse/expand
  toggleFilters() {
    this.showFilters = !this.showFilters;
  }


  // Accordion toggle method
  toggleAdvertAccordion(advert: AdvertNotificationStatsDTO) {
    advert.isOpen = !advert.isOpen;
  }

  // Check if advert accordion is open
  isAdvertOpen(advert: AdvertNotificationStatsDTO): boolean {
    return advert.isOpen;
  }

  // Customer breakdown modal
  showCustomerBreakdown(advertId: string, action: 'delivered' | 'opened' | 'clicked') {
    // This would open a modal or navigate to a detailed view
    // For now, just show an alert
    alert(`Would show customers who ${action} emails for advert ${advertId}`);
  }
}
