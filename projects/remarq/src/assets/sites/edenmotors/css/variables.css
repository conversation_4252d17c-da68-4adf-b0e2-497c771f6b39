:root {

  --font1: "Poppins";
  --font2: "Poppins";
  --font3: "<PERSON>";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);
  --footerHeight: 118px;
  --headerHeight: 64px;
  /* Palette */
  --colour1: #002239;
  --colour2: #DD1921;
  --colour3: #fff;
  --colour5: #002239;
  --colour7: #002239;

  --textLabelColour: #999;

  --primaryColour: var(--colour1);
  --navbarBgColour: #fff;
  --textColour: var(--colour1);
  --navbarTextColour: #222;
  --bgColour: #f0f1f2;
  --attentionBoxBackgroundColour: var(--colour2);
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --sideNavBackgroundColour: #fff;
  --sideNavTextColour: var(--colour1);
  --footerBackgroundColour: #222;
  --footerTextColour: #fff;
  --inputBorderColour: #ced4da;
  --softInputBorderColour: #ced4da;
  --navSearchBgColour: #f4f4f4;
  --primaryButtonColour: var(--colour1);
  --secondaryButtonColour: var(--colour1);
  --underNavTextColour: var(--colour1);
  --underNavBgColour: #fff;


  --buyNowColour: #FFB154;
  --timedSaleColour: #51ADDF;
  --managedSaleColour: #606DE4;
  --sideNavTextColour: var(--colour1);
  --sideNavHoverTextColour: var(--colour2);
  --switchColour: var(--colour3);
  --linkColour: var(--colour1);
  --attentionBoxBackgroundColour: #e8e8e8;
  --headerColour: var(--colour1);

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}



