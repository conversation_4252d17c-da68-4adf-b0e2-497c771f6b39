@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@300;700&display=swap');

#site-edenmotors {

  .header-line-3 {
    color: #DD1921;
  }

  .undernav {
    box-shadow: 0 0 5px #00000029 !important;
    a.undernav-item { font-weight: 600;}
  }

  .nav-label {
    font-weight: 500;
    font-size: 0.875rem;
  }

  .upcoming-sales {
    color: #fff !important;
  }

  .navbar-brand {
    background-image: url('/assets/sites/edenmotors/images/edenlogo.png');
    background-repeat: no-repeat;
    background-size: contain;
    height: 45px;
    width: 170px;
    margin-right: 0;
  }

  .homepage-car {
    background-image: url("/assets/sites/edenmotors/images/mokka.png");
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
  }

  .search-submit {
    background-color: #ddd;
  }

  footer {
    color: #bbb !important;

    &.page-footer a, &.page-footer a:visited {
      color: #fff !important;
    }
  }

  .btn-login {
    background-color: #111 !important;
    color: #fff !important;
  }

  .btn-sign-up {
    background-color: #dd1921;
    color: #fff;
  }

  .selected {
    background-color: #eee !important;
  }

  .modal-header {
    background-color: #333 !important;
    color: #fff !important;
  }

  .modal-body .form-control {
    background-color: #fff !important;
  }

  .btn-secondary:hover {
    border: 1px solid var(--colour1);
    background-color: #fff;
    color: var(--colour4);
  }
}
