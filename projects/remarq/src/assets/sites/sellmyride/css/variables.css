:root {

  --font1: "Prompt";
  --font2: "Prompt";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);

  /* Palette */
  --colour1: #00002C;
  --colour2: #FF497B;
  --colour3: #00BDF2;
  --inputBorderColour: #b3cad6;

  --textLabelColour: #667A88;
  --navbarBgColour: #fff;
  --textColour: var(--colour1);
  --navbarTextColour: var(--textColour);
  --bgColour: #f0f1f2;
  --headerHeight: 63px;
  --attentionBoxBackgroundColour: #dee2e6;
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --topbarIconColour: var(--colour3);
  --errorColour: #c00;
  --vrmBackgroundColour: #fff6c3;
  --sideNavBackgroundColour: #fff;
  --sideNavHoverTextColour: var(--colour2);
  --sideNavHoverBackgroundColour: #eee;
  --sideNavTextColour: var(--colour1);
  --linkColour: var(--colour1);
  --primaryButtonColour: var(--colour1);
  --secondaryButtonColour: var(--colour1);


  --successColour: forestgreen;
  --buyNowColour: #314d5f;
  --timedSaleColour: #0e2839;
  --managedSaleColour: #1e3b4e;
  --underwriteSaleColour: #476375;

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}


